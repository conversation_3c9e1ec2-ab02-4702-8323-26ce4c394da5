import { NextResponse } from 'next/server'

const historyItems = [
  {
    id: "1",
    title: "声誉风险变化趋势及主要影响因素分析",
    timestamp: new Date().toISOString(),
    category: "风险分析"
  },
  {
    id: "2",
    title: "网络舆情负面事件影响评估",
    timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(), // 2小时前
    category: "舆情监测"
  },
  {
    id: "3",
    title: "监管处罚对品牌形象的影响分析",
    timestamp: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(), // 昨天
    category: "监管风险"
  },
  {
    id: "4",
    title: "客户投诉声誉风险预警报告",
    timestamp: new Date(Date.now() - 24 * 60 * 60 * 1000 - 5 * 60 * 60 * 1000).toISOString(), // 昨天早些时候
    category: "客户关系"
  },
  {
    id: "5",
    title: "ESG评级下调风险影响分析",
    timestamp: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(), // 3天前
    category: "ESG风险"
  },
  {
    id: "6",
    title: "同业声誉事件传染风险评估",
    timestamp: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(), // 5天前
    category: "行业风险"
  }
]

export async function GET() {
  try {
    return NextResponse.json(historyItems)
  } catch (error) {
    console.error('API Error:', error)
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 })
  }
}
