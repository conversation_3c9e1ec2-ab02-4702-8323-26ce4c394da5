// 简单的API测试脚本
async function testAPIs() {
  const baseUrl = 'http://localhost:3000/api/agent'
  
  console.log('🧪 开始测试API端点...\n')
  
  try {
    // 测试系统信息
    console.log('📋 测试系统信息API...')
    const systemResponse = await fetch(`${baseUrl}/system-info`)
    const systemData = await systemResponse.json()
    console.log('✅ 系统信息:', systemData.title)
    
    // 测试快捷操作
    console.log('\n🚀 测试快捷操作API...')
    const actionsResponse = await fetch(`${baseUrl}/quick-actions`)
    const actionsData = await actionsResponse.json()
    console.log(`✅ 快捷操作: 获取到 ${actionsData.length} 个操作`)
    
    // 测试历史记录
    console.log('\n📚 测试历史记录API...')
    const historyResponse = await fetch(`${baseUrl}/history`)
    const historyData = await historyResponse.json()
    console.log(`✅ 历史记录: 获取到 ${historyData.length} 条记录`)
    
    // 测试图表数据
    console.log('\n📊 测试图表数据API...')
    const chartResponse = await fetch(`${baseUrl}/chart-data`)
    const chartData = await chartResponse.json()
    console.log(`✅ 图表数据: 获取到 ${chartData.length} 个数据点`)
    
    // 测试表格数据
    console.log('\n📋 测试表格数据API...')
    const tableResponse = await fetch(`${baseUrl}/table-data`)
    const tableData = await tableResponse.json()
    console.log(`✅ 表格数据: 获取到 ${tableData.length} 行数据`)
    
    // 测试流式接口
    console.log('\n🌊 测试流式接口API...')
    const streamResponse = await fetch(baseUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        query: '请分析当前的声誉风险状况',
        stream: true
      }),
    })

    if (streamResponse.ok) {
      console.log('✅ 流式接口: 连接成功')
      const reader = streamResponse.body.getReader()
      const decoder = new TextDecoder()
      let chunks = 0
      let progressEvents = []

      while (true) {
        const { done, value } = await reader.read()
        if (done) break

        const chunk = decoder.decode(value)
        chunks++

        // 解析进度事件
        const lines = chunk.split('\n')
        for (const line of lines) {
          if (line.startsWith('data: ')) {
            try {
              const data = JSON.parse(line.slice(6))
              if (data.type === 'data' && data.progressEvent) {
                progressEvents.push(data.progressEvent)
                console.log(`   📊 进度事件: 步骤${data.progressEvent.step} - ${data.progressEvent.message}`)
              }
            } catch (e) {
              // 忽略解析错误
            }
          }
        }

        if (chunks <= 3) { // 只显示前3个chunk的原始数据
          console.log(`   📦 接收到数据块 ${chunks}:`, chunk.substring(0, 50) + '...')
        }
      }
      console.log(`✅ 流式接口: 总共接收到 ${chunks} 个数据块，${progressEvents.length} 个进度事件`)
    }
    
    console.log('\n🎉 所有API测试完成！')
    
  } catch (error) {
    console.error('❌ 测试失败:', error.message)
  }
}

// 运行测试
testAPIs()
