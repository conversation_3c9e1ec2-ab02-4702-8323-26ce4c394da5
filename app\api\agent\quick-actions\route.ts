import { NextResponse } from 'next/server'

const quickActions = [
  {
    id: 1,
    title: "监管处罚事件影响评估",
    description: "分析监管处罚对机构声誉的影响程度",
    icon: "AlertTriangle",
    category: "监管风险",
    query: "请分析最近的监管处罚事件对我们机构声誉的影响程度和应对策略",
  },
  {
    id: 2,
    title: "网络舆情负面监测",
    description: "实时监测网络舆情中的负面信息",
    icon: "Globe",
    category: "舆情监测",
    query: "请帮我监测和分析当前网络舆情中关于我们机构的负面信息",
  },
  {
    id: 3,
    title: "客户投诉声誉风险",
    description: "评估客户投诉对品牌形象的损害",
    icon: "MessageCircle",
    category: "客户关系",
    query: "请分析近期客户投诉情况对我们品牌声誉造成的风险",
  },
  {
    id: 4,
    title: "同业声誉事件传染",
    description: "分析同业负面事件的传染风险",
    icon: "Building2",
    category: "行业风险",
    query: "请分析同业最近发生的声誉事件对我们机构可能产生的传染风险",
  },
  {
    id: 5,
    title: "媒体报道情感分析",
    description: "分析主流媒体对机构的报道情感",
    icon: "Newspaper",
    category: "媒体监测",
    query: "请分析主流媒体近期对我们机构的报道情感倾向和影响",
  },
  {
    id: 6,
    title: "ESG评级影响评估",
    description: "评估ESG评级变化对声誉的影响",
    icon: "BarChart",
    category: "ESG风险",
    query: "请评估我们机构ESG评级变化对整体声誉的影响程度",
  },
  {
    id: 7,
    title: "利益相关者情绪追踪",
    description: "追踪各利益相关者的情绪变化",
    icon: "Users",
    category: "关系管理",
    query: "请帮我追踪和分析各利益相关者对我们机构的情绪变化趋势",
  },
  {
    id: 8,
    title: "声誉危机预警系统",
    description: "建立声誉风险的早期预警机制",
    icon: "Bell",
    category: "预警系统",
    query: "请帮我建立一套完整的声誉风险早期预警机制和应对流程",
  },
]

export async function GET() {
  try {
    return NextResponse.json(quickActions)
  } catch (error) {
    console.error('API Error:', error)
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 })
  }
}
