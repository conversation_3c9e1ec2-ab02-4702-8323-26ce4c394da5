import { NextResponse } from 'next/server'

const chartData = [
  { month: "2025-01", value: 78.5 },
  { month: "2025-02", value: 76.2 },
  { month: "2025-03", value: 72.8 },
  { month: "2025-04", value: 69.3 },
  { month: "2025-05", value: 65.7 },
  { month: "2025-06", value: 63.4 },
  { month: "2025-07", value: 61.2 },
]

export async function GET() {
  try {
    return NextResponse.json(chartData)
  } catch (error) {
    console.error('API Error:', error)
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 })
  }
}
