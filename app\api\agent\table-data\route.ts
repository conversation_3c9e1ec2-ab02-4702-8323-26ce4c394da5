import { NextResponse } from 'next/server'

const tableData = [
  {
    time: "2025-07",
    reputationIndex: "61.2",
    negativeEvents: "23",
    mediaExposure: "1,247",
    stakeholderSentiment: "负面",
  },
  {
    time: "2025-06",
    reputationIndex: "63.4",
    negativeEvents: "19",
    mediaExposure: "1,156",
    stakeholderSentiment: "负面",
  },
  {
    time: "2025-05",
    reputationIndex: "65.7",
    negativeEvents: "16",
    mediaExposure: "1,089",
    stakeholderSentiment: "中性",
  },
  {
    time: "2025-04",
    reputationIndex: "69.3",
    negativeEvents: "12",
    mediaExposure: "987",
    stakeholderSentiment: "中性",
  },
  {
    time: "2025-03",
    reputationIndex: "72.8",
    negativeEvents: "8",
    mediaExposure: "856",
    stakeholderSentiment: "中性",
  },
  {
    time: "2025-02",
    reputationIndex: "76.2",
    negativeEvents: "5",
    mediaExposure: "743",
    stakeholderSentiment: "正面",
  },
  {
    time: "2025-01",
    reputationIndex: "78.5",
    negativeEvents: "3",
    mediaExposure: "692",
    stakeholderSentiment: "正面",
  },
]

export async function GET() {
  try {
    return NextResponse.json(tableData)
  } catch (error) {
    console.error('API Error:', error)
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 })
  }
}
