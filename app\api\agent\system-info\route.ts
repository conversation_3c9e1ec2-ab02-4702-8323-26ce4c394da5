import { NextResponse } from 'next/server'

const systemInfo = {
  title: "声誉风险助手",
  greeting: "今日你好",
  subtitle: "我能为你做什么？",
  description: "声誉风险助手是专为金融机构设计的智能风险分析系统。我可以帮助您实时监测声誉风险、分析负面事件影响、评估媒体舆情、追踪利益相关者情绪变化，并提供专业的风险应对策略。通过AI驱动的多维度分析，为您的机构声誉管理提供全方位的决策支持。"
}

export async function GET() {
  try {
    return NextResponse.json(systemInfo)
  } catch (error) {
    console.error('API Error:', error)
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 })
  }
}
