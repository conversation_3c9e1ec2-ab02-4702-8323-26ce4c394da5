import { NextRequest, NextResponse } from 'next/server'

// 模拟数据
const mockChartData = [
  { month: "2025-01", value: 78.5 },
  { month: "2025-02", value: 76.2 },
  { month: "2025-03", value: 72.8 },
  { month: "2025-04", value: 69.3 },
  { month: "2025-05", value: 65.7 },
  { month: "2025-06", value: 63.4 },
  { month: "2025-07", value: 61.2 },
]

const mockTableData = [
  {
    time: "2025-07",
    reputationIndex: "61.2",
    negativeEvents: "23",
    mediaExposure: "1,247",
    stakeholderSentiment: "负面",
  },
  {
    time: "2025-06",
    reputationIndex: "63.4",
    negativeEvents: "19",
    mediaExposure: "1,156",
    stakeholderSentiment: "负面",
  },
  {
    time: "2025-05",
    reputationIndex: "65.7",
    negativeEvents: "16",
    mediaExposure: "1,089",
    stakeholderSentiment: "中性",
  },
]

// 模拟进度事件数据
const mockProgressEvents = [
  {
    timestamp: "2025-08-06T10:30:00.123Z",
    agent: "supervisor",
    event_type: "progress",
    step: 1,
    progress: 25,
    status: "completed",
    message: "查询意图分析完成，启动完整流程",
    icon: "✅"
  },
  {
    timestamp: "2025-08-06T10:30:02.789Z",
    agent: "supervisor",
    event_type: "progress",
    step: 2,
    progress: 50,
    status: "completed",
    message: "系统状态分析完成",
    icon: "✅"
  },
  {
    timestamp: "2025-08-06T10:30:04.012Z",
    agent: "data_collector",
    event_type: "progress",
    step: 3,
    progress: 75,
    status: "processing",
    message: "正在进行声誉风险评估分析...",
    icon: "🔍"
  },
  {
    timestamp: "2025-08-06T10:30:06.345Z",
    agent: "data_collector",
    event_type: "progress",
    step: 3,
    progress: 75,
    status: "completed",
    message: "声誉风险评估分析完成",
    icon: "✅"
  },
  {
    timestamp: "2025-08-06T10:30:08.678Z",
    agent: "report_generator",
    event_type: "progress",
    step: 4,
    progress: 100,
    status: "completed",
    message: "风险报告生成完成",
    icon: "📊"
  }
]

// 模拟流式响应
async function* generateStreamResponse(query: string) {
  const responses = [
    "正在分析您的声誉风险查询...",
    "已获取相关数据，开始进行风险评估...",
    "根据分析结果，当前声誉风险指数为61.2，处于中等风险水平。",
    "主要风险因素包括：1) 负面媒体报道增加 2) 监管处罚事件 3) 客户投诉上升",
    "建议采取以下应对措施：加强舆情监测、完善风险预警机制、提升客户服务质量。"
  ]

  // 发送内容响应
  for (let i = 0; i < responses.length; i++) {
    yield `data: ${JSON.stringify({ type: 'content', content: responses[i] })}\n\n`
    await new Promise(resolve => setTimeout(resolve, 1000))
  }

  // 发送进度事件
  for (let i = 0; i < mockProgressEvents.length; i++) {
    yield `data: ${JSON.stringify({
      type: 'data',
      progressEvent: mockProgressEvents[i]
    })}\n\n`
    await new Promise(resolve => setTimeout(resolve, 500))
  }

  // 发送结构化数据
  yield `data: ${JSON.stringify({
    type: 'data',
    chartData: mockChartData,
    tableData: mockTableData
  })}\n\n`
}

export async function POST(request: NextRequest) {
  try {
    const { query, stream } = await request.json()

    if (stream) {
      // 流式响应
      const encoder = new TextEncoder()
      const stream = new ReadableStream({
        async start(controller) {
          try {
            for await (const chunk of generateStreamResponse(query)) {
              controller.enqueue(encoder.encode(chunk))
            }
            controller.close()
          } catch (error) {
            controller.error(error)
          }
        }
      })

      return new Response(stream, {
        headers: {
          'Content-Type': 'text/plain; charset=utf-8',
          'Cache-Control': 'no-cache',
          'Connection': 'keep-alive',
        },
      })
    } else {
      // 普通响应
      return NextResponse.json({
        response: "这是一个基于您查询的声誉风险分析结果。",
        chartData: mockChartData,
        tableData: mockTableData,
        progressEvents: mockProgressEvents
      })
    }
  } catch (error) {
    console.error('API Error:', error)
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 })
  }
}
