// Debug启动脚本
const { spawn } = require('child_process');

console.log('🐛 启动Debug模式...');

// 设置环境变量
process.env.NODE_OPTIONS = '--inspect=0.0.0.0:9230';

// 启动Next.js开发服务器
const nextProcess = spawn('npm', ['run', 'dev'], {
  stdio: 'inherit',
  shell: true,
  env: {
    ...process.env,
    NODE_OPTIONS: '--inspect=0.0.0.0:9230'
  }
});

console.log('🔍 Debug服务器启动中...');
console.log('📍 调试端口: 9230');
console.log('🌐 应用地址: http://localhost:3000');
console.log('🐛 Chrome DevTools: chrome://inspect');

nextProcess.on('close', (code) => {
  console.log(`\n🛑 服务器已停止，退出代码: ${code}`);
});

nextProcess.on('error', (error) => {
  console.error('❌ 启动失败:', error);
});

// 优雅关闭
process.on('SIGINT', () => {
  console.log('\n🛑 正在关闭服务器...');
  nextProcess.kill('SIGINT');
});
