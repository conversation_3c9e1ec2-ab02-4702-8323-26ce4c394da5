"use client"

import { useState, useEffect, useRef, useCallback } from "react"
import React from 'react';
import {
  <PERSON>Off,
  Loader2,
  CheckCircle,
  ChevronDown,
  ChevronRight,
  Paperclip,
  Languages,
  Mic,
  Send,
  Copy,
  Quote,
  RotateCcw,
  ThumbsUp,
  ThumbsDown,
  FileText,
  TrendingUp,
  BarChart3,
  Download,
  Info,
  ChevronUp,
  AlertTriangle,
  Shield,
  Eye,
  Newspaper,
  Users,
  Building2,
  Globe,
  MessageCircle,
  BarChart,
  Bell,
} from "lucide-react"
import { XAxis, YAxis, CartesianGrid, ResponsiveContainer, Area, AreaChart } from "recharts"

// 数据类型定义
interface ChatMessage {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  timestamp: Date;
}

interface ChartDataPoint {
  month: string;
  value: number;
}

interface TableDataRow {
  time: string;
  reputationIndex: string;
  negativeEvents: string;
  mediaExposure: string;
  stakeholderSentiment: string;
}

interface QuickAction {
  id: number;
  title: string;
  description: string;
  icon: React.ReactNode;
  category: string;
  query: string;
}

interface HistoryItem {
  id: string;
  title: string;
  timestamp: string;
  category: string;
}

interface ProgressEvent {
  timestamp: string;
  agent: string;
  event_type: string;
  step: number;
  progress: number;
  status: 'processing' | 'completed' | 'error';
  message: string;
  icon: string;
}

interface ResearchSession {
  id: string;
  query: string;
  events: ProgressEvent[];
  currentStep: number;
  totalSteps: number;
  overallProgress: number;
  status: 'active' | 'completed' | 'error';
}

// 声誉风险评估相关类型定义
interface ModuleStatusEvent {
  status: 'starting' | 'analyzing' | 'initialized';
  name: string;
  progress: number;
}

interface AgentEvent {
  agent: string;
  status: 'started' | 'completed';
  message: string;
  timestamp: string;
  data?: {
    planning_summary?: string;
    tasks_count?: number;
    risk_dimensions?: string[];
    entities_count?: number;
    sentiment_data_count?: number;
    collection_status?: string;
    processed_data?: any[];
    chart_data?: any;
    processed_data_count?: number;
    risk_factors_count?: number;
    sentiment_distribution?: any;
    risk_level?: string;
    analysis_conclusion?: string;
    report_status?: string;
    executive_summary?: string;
    final_report?: any;
  };
}

interface StreamEvent {
  event?: string | AgentEvent;
  status?: string;
  name?: string;
  progress?: number;
  error?: string;
}

export default function ReputationRiskInterface() {
  const [hideThinkPanel, setHideThinkPanel] = useState(false)

  const [selectedMetric, setSelectedMetric] = useState("声誉指数")
  const [message, setMessage] = useState("")
  const [windowWidth, setWindowWidth] = useState(0)
  const [showHistorySidebar, setShowHistorySidebar] = useState(false)
  const [isStarted, setIsStarted] = useState(false)

  // 动态数据状态
  const [chartData, setChartData] = useState<ChartDataPoint[]>([])
  const [tableData, setTableData] = useState<TableDataRow[]>([])
  const [quickActions, setQuickActions] = useState<QuickAction[]>([])
  const [researchSessions, setResearchSessions] = useState<ResearchSession[]>([])
  const [currentSession, setCurrentSession] = useState<ResearchSession | null>(null)
  const [historyItems, setHistoryItems] = useState<HistoryItem[]>([])
  const [chatMessages, setChatMessages] = useState<ChatMessage[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [streamingContent, setStreamingContent] = useState("")
  const [systemInfo, setSystemInfo] = useState({
    title: "声誉风险助手",
    greeting: "今日你好",
    subtitle: "我能为你做什么？",
    description: "声誉风险助手是专为金融机构设计的智能风险分析系统。我可以帮助您实时监测声誉风险、分析负面事件影响、评估媒体舆情、追踪利益相关者情绪变化，并提供专业的风险应对策略。通过AI驱动的多维度分析，为您的机构声誉管理提供全方位的决策支持。"
  })

  // Handle window resize for responsive layout
  useEffect(() => {
    const handleResize = () => {
      setWindowWidth(window.innerWidth)
    }

    handleResize()
    window.addEventListener("resize", handleResize)
    return () => window.removeEventListener("resize", handleResize)
  }, [])

  // Auto-hide think panel when screen is too narrow
  const shouldShowThinkPanel = !hideThinkPanel && windowWidth > 1200

  // 流式数据处理函数
  const processStreamData = async (userQuery: string): Promise<string> => {
    setIsLoading(true)
    setStreamingContent("")
    let finalContent = ""

    try {
      const response = await fetch('/app/chat/chat2shengyuAgent', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ query: userQuery, stream: true }),
      })

      if (!response.ok) {
        throw new Error('Network response was not ok')
      }

      const reader = response.body?.getReader()
      const decoder = new TextDecoder()

      if (reader) {
        while (true) {
          const { done, value } = await reader.read()
          if (done) break

          const chunk = decoder.decode(value)
          const lines = chunk.split('\n')

          for (const line of lines) {
            if (line.trim() === '') continue

            // 处理 Server-Sent Events 格式
            if (line.startsWith('event: ')) {
              // 跳过事件类型行，我们主要关注数据内容
              continue
            }

            if (line.startsWith('data: ')) {
              try {
                const dataStr = line.slice(6).trim()

                // 处理不同类型的事件数据
                if (dataStr === '声誉风险评估完成') {
                  // 处理结束事件
                  setIsLoading(false)
                  setCurrentSession(prevSession => {
                    if (!prevSession) return prevSession
                    return {
                      ...prevSession,
                      status: 'completed' as const,
                      overallProgress: 100
                    }
                  })
                  continue
                }

                const data = JSON.parse(dataStr)

                // 处理模块状态事件
                if (data.status && data.name && data.progress !== undefined) {
                  console.log('📊 接收到模块状态:', data)

                  // 更新流式内容显示
                  const newContent = `${data.name}: ${data.status}\n`
                  setStreamingContent(prev => prev + newContent)
                  finalContent += newContent

                  // 创建进度事件并更新会话
                  const progressEvent: ProgressEvent = {
                    timestamp: new Date().toISOString(),
                    agent: data.name,
                    event_type: 'moduleStatus',
                    step: Math.floor(data.progress / 25) + 1, // 将进度转换为步骤
                    progress: data.progress,
                    status: data.status === 'starting' ? 'processing' :
                           data.status === 'analyzing' ? 'processing' :
                           data.status === 'initialized' ? 'completed' : 'processing',
                    message: `${data.name}: ${data.status}`,
                    icon: data.status === 'starting' ? '🚀' :
                          data.status === 'analyzing' ? '🔍' :
                          data.status === 'initialized' ? '✅' : '⚙️'
                  }

                  setCurrentSession(prevSession => {
                    if (!prevSession) return prevSession
                    return {
                      ...prevSession,
                      events: [...prevSession.events, progressEvent],
                      currentStep: progressEvent.step,
                      overallProgress: data.progress,
                      status: 'active' as const
                    }
                  })
                }

                // 处理智能体事件
                if (data.event) {
                  console.log('🤖 接收到智能体事件:', data)

                  let agentEvent
                  try {
                    // 解析嵌套的事件字符串 - 使用安全的JSON解析
                    if (typeof data.event === 'string') {
                      // 处理类似 "{'agent': 'planning', 'status': 'started', ...}" 格式的字符串
                      // 将单引号替换为双引号以符合JSON格式
                      const jsonStr = data.event.replace(/'/g, '"')
                      agentEvent = JSON.parse(jsonStr)
                    } else {
                      agentEvent = data.event
                    }
                  } catch (e) {
                    console.error('解析智能体事件失败:', e, '原始数据:', data.event)
                    continue
                  }

                  // 更新流式内容显示
                  const newContent = `${agentEvent.agent}: ${agentEvent.message}\n`
                  setStreamingContent(prev => prev + newContent)
                  finalContent += newContent

                  // 创建进度事件 - 根据智能体类型计算步骤和进度
                  let step = 1
                  let progress = 20

                  // 根据智能体类型确定步骤
                  switch (agentEvent.agent) {
                    case 'planning':
                      step = 1
                      progress = agentEvent.status === 'started' ? 15 : 25
                      break
                    case 'data_collection':
                      step = 2
                      progress = agentEvent.status === 'started' ? 30 : 50
                      break
                    case 'risk_analysis':
                      step = 3
                      progress = agentEvent.status === 'started' ? 55 : 75
                      break
                    case 'report_generation':
                      step = 4
                      progress = agentEvent.status === 'started' ? 80 : 95
                      break
                    default:
                      step = 1
                      progress = agentEvent.status === 'started' ? 10 : 20
                  }

                  const progressEvent: ProgressEvent = {
                    timestamp: agentEvent.timestamp || new Date().toISOString(),
                    agent: agentEvent.agent,
                    event_type: 'agent',
                    step: step,
                    progress: progress,
                    status: agentEvent.status === 'started' ? 'processing' :
                           agentEvent.status === 'completed' ? 'completed' : 'processing',
                    message: agentEvent.message,
                    icon: agentEvent.status === 'started' ? '🚀' :
                          agentEvent.status === 'completed' ? '✅' : '⚙️'
                  }

                  setCurrentSession(prevSession => {
                    if (!prevSession) return prevSession
                    return {
                      ...prevSession,
                      events: [...prevSession.events, progressEvent],
                      currentStep: progressEvent.step,
                      overallProgress: progressEvent.progress,
                      status: 'active' as const
                    }
                  })

                  // 如果有数据，处理结构化数据
                  if (agentEvent.data) {
                    // 处理规划数据
                    if (agentEvent.data.planning_summary) {
                      const content = `\n规划摘要: ${agentEvent.data.planning_summary}\n`
                      setStreamingContent(prev => prev + content)
                      finalContent += content
                    }

                    // 处理数据收集结果
                    if (agentEvent.data.entities_count !== undefined) {
                      const content = `\n收集到 ${agentEvent.data.entities_count} 个实体，${agentEvent.data.sentiment_data_count} 条情感数据\n`
                      setStreamingContent(prev => prev + content)
                      finalContent += content
                    }

                    // 处理风险分析数据
                    if (agentEvent.data.processed_data && agentEvent.data.processed_data.length > 0) {
                      const content = `\n分析了 ${agentEvent.data.processed_data_count} 条数据，风险等级: ${agentEvent.data.risk_level}\n`
                      setStreamingContent(prev => prev + content)
                      finalContent += content

                      // 如果有图表数据，更新图表
                      if (agentEvent.data.chart_data) {
                        // 这里可以根据实际的图表数据格式来更新
                        console.log('收到图表数据:', agentEvent.data.chart_data)
                      }
                    }

                    // 处理最终报告数据
                    if (agentEvent.data.final_report) {
                      const report = agentEvent.data.final_report
                      let content = `\n生成报告完成: ${report.report_type}\n`
                      setStreamingContent(prev => prev + content)
                      finalContent += content

                      // 更新流式内容显示报告摘要
                      if (report.sections && report.sections.executive_summary) {
                        const summaryContent = `\n执行摘要: ${report.sections.executive_summary.substring(0, 200)}...\n`
                        setStreamingContent(prev => prev + summaryContent)
                        finalContent += summaryContent
                      }

                      // 如果有风险评估数据，可以更新相关状态
                      if (report.sections && report.sections.risk_assessment) {
                        const riskAssessment = report.sections.risk_assessment
                        const riskContent = `\n风险评估: ${riskAssessment.risk_level} (评分: ${riskAssessment.risk_score})\n`
                        setStreamingContent(prev => prev + riskContent)
                        finalContent += riskContent
                      }
                    }
                  }
                }

                // 处理错误事件
                if (data.error) {
                  console.error('❌ 接收到错误事件:', data.error)
                  const errorContent = `错误: ${data.error}\n`
                  setStreamingContent(prev => prev + errorContent)
                  finalContent += errorContent

                  setCurrentSession(prevSession => {
                    if (!prevSession) return prevSession
                    return {
                      ...prevSession,
                      status: 'error' as const
                    }
                  })
                }

              } catch (e) {
                console.error('Error parsing stream data:', e, 'Raw data:', line)
              }
            }
          }
        }
      }
    } catch (error) {
      console.error('Error fetching stream data:', error)
      const errorContent = `连接错误: ${error}\n`
      setStreamingContent(prev => prev + errorContent)
      finalContent += errorContent

      setCurrentSession(prevSession => {
        if (!prevSession) return prevSession
        return {
          ...prevSession,
          status: 'error' as const
        }
      })
    } finally {
      setIsLoading(false)
    }

    return finalContent
  }

  // 获取初始数据
  useEffect(() => {
    const fetchInitialData = async () => {
      try {
        // 获取系统信息
        const systemResponse = await fetch('/api/agent/system-info')
        if (systemResponse.ok) {
          const system = await systemResponse.json()
          setSystemInfo(system)
        }

        // 获取快捷操作
        const actionsResponse = await fetch('/api/agent/quick-actions')
        if (actionsResponse.ok) {
          const actions = await actionsResponse.json()
          setQuickActions(actions)
        }

        // 获取历史记录
        const historyResponse = await fetch('/api/agent/history')
        if (historyResponse.ok) {
          const history = await historyResponse.json()
          setHistoryItems(history)
        }

        // 获取基础图表数据
        const chartResponse = await fetch('/api/agent/chart-data')
        if (chartResponse.ok) {
          const chart = await chartResponse.json()
          setChartData(chart)
        }

        // 获取表格数据
        const tableResponse = await fetch('/api/agent/table-data')
        if (tableResponse.ok) {
          const table = await tableResponse.json()
          setTableData(table)
        }
      } catch (error) {
        console.error('Error fetching initial data:', error)
        // 如果API不可用，使用默认数据
        loadDefaultData()
      }
    }

    fetchInitialData()
  }, [])

  // 默认数据加载函数（作为后备方案）
  const loadDefaultData = () => {
    setChartData([
      { month: "2025-01", value: 78.5 },
      { month: "2025-02", value: 76.2 },
      { month: "2025-03", value: 72.8 },
      { month: "2025-04", value: 69.3 },
      { month: "2025-05", value: 65.7 },
      { month: "2025-06", value: 63.4 },
      { month: "2025-07", value: 61.2 },
    ])

    setTableData([
      {
        time: "2025-07",
        reputationIndex: "61.2",
        negativeEvents: "23",
        mediaExposure: "1,247",
        stakeholderSentiment: "负面",
      },
      {
        time: "2025-06",
        reputationIndex: "63.4",
        negativeEvents: "19",
        mediaExposure: "1,156",
        stakeholderSentiment: "负面",
      },
      {
        time: "2025-05",
        reputationIndex: "65.7",
        negativeEvents: "16",
        mediaExposure: "1,089",
        stakeholderSentiment: "中性",
      },
      {
        time: "2025-04",
        reputationIndex: "69.3",
        negativeEvents: "12",
        mediaExposure: "987",
        stakeholderSentiment: "中性",
      },
      {
        time: "2025-03",
        reputationIndex: "72.8",
        negativeEvents: "8",
        mediaExposure: "856",
        stakeholderSentiment: "中性",
      },
      {
        time: "2025-02",
        reputationIndex: "76.2",
        negativeEvents: "5",
        mediaExposure: "743",
        stakeholderSentiment: "正面",
      },
      {
        time: "2025-01",
        reputationIndex: "78.5",
        negativeEvents: "3",
        mediaExposure: "692",
        stakeholderSentiment: "正面",
      },
    ])

    setQuickActions([
      {
        id: 1,
        title: "监管处罚事件影响评估",
        description: "分析监管处罚对机构声誉的影响程度",
        icon: <AlertTriangle className="w-5 h-5" />,
        category: "监管风险",
        query: "请分析最近的监管处罚事件对我们机构声誉的影响程度和应对策略",
      },
      {
        id: 2,
        title: "网络舆情负面监测",
        description: "实时监测网络舆情中的负面信息",
        icon: <Globe className="w-5 h-5" />,
        category: "舆情监测",
        query: "请帮我监测和分析当前网络舆情中关于我们机构的负面信息",
      },
      {
        id: 3,
        title: "客户投诉声誉风险",
        description: "评估客户投诉对品牌形象的损害",
        icon: <MessageCircle className="w-5 h-5" />,
        category: "客户关系",
        query: "请分析近期客户投诉情况对我们品牌声誉造成的风险",
      },
      {
        id: 4,
        title: "同业声誉事件传染",
        description: "分析同业负面事件的传染风险",
        icon: <Building2 className="w-5 h-5" />,
        category: "行业风险",
        query: "请分析同业最近发生的声誉事件对我们机构可能产生的传染风险",
      },
      {
        id: 5,
        title: "媒体报道情感分析",
        description: "分析主流媒体对机构的报道情感",
        icon: <Newspaper className="w-5 h-5" />,
        category: "媒体监测",
        query: "请分析主流媒体近期对我们机构的报道情感倾向和影响",
      },
      {
        id: 6,
        title: "ESG评级影响评估",
        description: "评估ESG评级变化对声誉的影响",
        icon: <BarChart className="w-5 h-5" />,
        category: "ESG风险",
        query: "请评估我们机构ESG评级变化对整体声誉的影响程度",
      },
      {
        id: 7,
        title: "利益相关者情绪追踪",
        description: "追踪各利益相关者的情绪变化",
        icon: <Users className="w-5 h-5" />,
        category: "关系管理",
        query: "请帮我追踪和分析各利益相关者对我们机构的情绪变化趋势",
      },
      {
        id: 8,
        title: "声誉危机预警系统",
        description: "建立声誉风险的早期预警机制",
        icon: <Bell className="w-5 h-5" />,
        category: "预警系统",
        query: "请帮我建立一套完整的声誉风险早期预警机制和应对流程",
      },
    ])

    setHistoryItems([
      {
        id: "1",
        title: "声誉风险变化趋势及主要影响因素分析",
        timestamp: "刚刚",
        category: "风险分析"
      },
      {
        id: "2",
        title: "网络舆情负面事件影响评估",
        timestamp: "2小时前",
        category: "舆情监测"
      },
      {
        id: "3",
        title: "监管处罚对品牌形象的影响分析",
        timestamp: "昨天 15:30",
        category: "监管风险"
      },
      {
        id: "4",
        title: "客户投诉声誉风险预警报告",
        timestamp: "昨天 10:15",
        category: "客户关系"
      },
      {
        id: "5",
        title: "ESG评级下调风险影响分析",
        timestamp: "3天前",
        category: "ESG风险"
      },
      {
        id: "6",
        title: "同业声誉事件传染风险评估",
        timestamp: "5天前",
        category: "行业风险"
      }
    ])

    // 设置默认的研究会话数据
    const defaultSession: ResearchSession = {
      id: "session-1",
      query: "请分析2025年1月到7月的声誉风险变化趋势及主要影响因素",
      currentStep: 3,
      totalSteps: 4,
      overallProgress: 75,
      status: 'active',
      events: [
        {
          timestamp: "2025-08-06T10:30:00.123Z",
          agent: "supervisor",
          event_type: "progress",
          step: 1,
          progress: 25,
          status: "completed",
          message: "查询意图分析完成，启动完整流程",
          icon: "✅"
        },
        {
          timestamp: "2025-08-06T10:30:02.789Z",
          agent: "supervisor",
          event_type: "progress",
          step: 2,
          progress: 50,
          status: "completed",
          message: "系统状态分析完成",
          icon: "✅"
        },
        {
          timestamp: "2025-08-06T10:30:04.012Z",
          agent: "data_collector",
          event_type: "progress",
          step: 3,
          progress: 75,
          status: "processing",
          message: "正在进行声誉风险评估分析...",
          icon: "🔍"
        }
      ]
    }

    setCurrentSession(defaultSession)
    setResearchSessions([defaultSession])
  }





  const handleSendMessage = async () => {
    if (message.trim()) {
      setIsStarted(true)

      // 创建新的研究会话
      const newSession: ResearchSession = {
        id: `session-${Date.now()}`,
        query: message,
        currentStep: 1,
        totalSteps: 4,
        overallProgress: 0,
        status: 'active',
        events: []
      }

      // 立即更新状态
      console.log('🔧 创建新会话:', newSession)
      setCurrentSession(newSession)
      setResearchSessions(prev => [...prev, newSession])
      console.log('✅ 会话状态已更新')

      // 添加用户消息到聊天记录
      const userMessage: ChatMessage = {
        id: Date.now().toString(),
        role: 'user',
        content: message,
        timestamp: new Date()
      }
      setChatMessages(prev => [...prev, userMessage])

      // 保存当前消息内容
      const currentMessage = message
      setMessage("") // 立即清空输入框

      // 开始流式处理
      const finalContent = await processStreamData(currentMessage)

      // 添加AI回复到聊天记录
      if (finalContent.trim()) {
        const aiMessage: ChatMessage = {
          id: (Date.now() + 1).toString(),
          role: 'assistant',
          content: finalContent,
          timestamp: new Date()
        }
        setChatMessages(prev => [...prev, aiMessage])
      }
    }
  }

  const handleQuickAction = async (action: QuickAction) => {
    setMessage(action.query)
    setIsStarted(true)

    // 创建新的研究会话
    const newSession: ResearchSession = {
      id: `session-${Date.now()}`,
      query: action.query,
      currentStep: 1,
      totalSteps: 4,
      overallProgress: 0,
      status: 'active',
      events: []
    }

    // 立即更新状态
    setCurrentSession(newSession)
    setResearchSessions(prev => [...prev, newSession])

    // 添加用户消息到聊天记录
    const userMessage: ChatMessage = {
      id: Date.now().toString(),
      role: 'user',
      content: action.query,
      timestamp: new Date()
    }
    setChatMessages(prev => [...prev, userMessage])

    // 开始流式处理
    const finalContent = await processStreamData(action.query)

    // 添加AI回复到聊天记录
    if (finalContent.trim()) {
      const aiMessage: ChatMessage = {
        id: (Date.now() + 1).toString(),
        role: 'assistant',
        content: finalContent,
        timestamp: new Date()
      }
      setChatMessages(prev => [...prev, aiMessage])
    }
  }

  // Welcome Screen Component
  const WelcomeScreen = React.memo(({
    message,
    onMessageChange,
    onSend
  }: {
    message: string;
    onMessageChange: (value: string) => void;
    onSend: () => void;
  }) => {
    const containerRef = useRef<HTMLDivElement>(null);
    const inputRef = useRef<HTMLInputElement>(null);
  
    // 稳定的事件处理函数
    const handleChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
      onMessageChange(e.target.value);
    }, []);
  
    // 保持输入框焦点
    useEffect(() => {
      inputRef.current?.focus();
    }, [message]);

    return (
      <div ref={containerRef} className="flex-1 overflow-y-auto p-8">
        <div className="max-w-4xl mx-auto">
          {/* Header */}
          <div className="text-center mb-12">
            <div className="flex items-center justify-center mb-6">
              <div className="w-16 h-16 bg-gradient-to-r from-red-500 to-orange-500 rounded-full flex items-center justify-center mb-4">
                <Shield className="w-8 h-8 text-white" />
              </div>
            </div>
            <h1 className="text-4xl font-bold text-gray-900 mb-4">
              {systemInfo.greeting}，<span className="text-red-600">{systemInfo.title}</span>
            </h1>
            <p className="text-xl text-gray-600 mb-8">{systemInfo.subtitle}</p>

            {/* System Introduction */}
            <div className="bg-gradient-to-r from-red-50 to-orange-50 border border-red-200 rounded-2xl p-6 mb-12">
              <h2 className="text-lg font-semibold text-gray-900 mb-3">系统介绍</h2>
              <p className="text-gray-700 leading-relaxed">
                {systemInfo.description}
              </p>
            </div>
          </div>

          {/* Quick Actions */}
          <div className="mb-8">
            <h3 className="text-lg font-semibold text-gray-900 mb-6 text-center">快捷操作</h3>
            <div className="grid grid-cols-4 gap-4">
              {quickActions.map((action) => (
                <button
                  key={action.id}
                  onClick={() => handleQuickAction(action)}
                  className="group bg-white border border-gray-200 rounded-xl p-4 hover:border-red-300 hover:shadow-lg transition-all duration-200 text-left"
                >
                  <div className="flex items-center mb-3">
                    <div className="w-10 h-10 bg-red-100 rounded-lg flex items-center justify-center text-red-600 group-hover:bg-red-200 transition-colors">
                      {action.icon}
                    </div>
                  </div>
                  <h4 className="font-medium text-gray-900 mb-2 group-hover:text-red-600 transition-colors">
                    {action.title}
                  </h4>
                  <p className="text-sm text-gray-600 mb-3">{action.description}</p>
                  <span className="inline-block px-2 py-1 text-xs bg-gray-100 text-gray-600 rounded-full">
                    {action.category}
                  </span>
                </button>
              ))}
            </div>
          </div>

          {/* Input Area */}
          <div className="bg-white border border-gray-200 rounded-2xl p-6 shadow-sm">
            <div className="flex items-center space-x-4">
              <input
                ref={inputRef}
                type="text"
                value={message}
                onChange={handleChange}
                placeholder="描述您的声誉风险分析需求或提问任何问题..."
                className="flex-1 bg-gray-50 border border-gray-200 rounded-xl px-4 py-3 text-gray-700 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent"
                onKeyDown={(e) => e.key === "Enter" && onSend()}
              />
              <button
                onClick={onSend}
                disabled={!message.trim()}
                className="px-6 py-3 bg-red-500 text-white rounded-xl hover:bg-red-600 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors flex items-center space-x-2"
              >
                <Send className="w-4 h-4" />
                <span>开始分析</span>
              </button>
            </div>

            {/* Input Tools */}
            <div className="flex items-center justify-between mt-4 pt-4 border-t border-gray-100">
              <div className="flex items-center space-x-3">
                <button className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors">
                  <Paperclip className="w-4 h-4" />
                </button>
                <button className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors">
                  <Languages className="w-4 h-4" />
                </button>
                <button className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors">
                  <Mic className="w-4 h-4" />
                </button>
              </div>
              <div className="text-xs text-gray-500">支持文件上传、语音输入和多语言分析</div>
            </div>
          </div>
        </div>
      </div>
    )
  })


  return (
    <div className="flex h-screen bg-white">
      {/* Left Conversation Panel */}
      <div
        className={`${shouldShowThinkPanel ? "w-2/3" : "w-full"} bg-white ${shouldShowThinkPanel ? "border-r border-gray-200" : ""} flex flex-col`}
      >
        {/* History Sidebar Overlay */}
        {showHistorySidebar && (
          <div className="fixed inset-0 bg-black bg-opacity-50 z-50" onClick={() => setShowHistorySidebar(false)}>
            <div className="w-80 h-full bg-white shadow-xl" onClick={(e) => e.stopPropagation()}>
              <div className="p-4 border-b border-gray-200">
                <div className="flex items-center justify-between">
                  <h2 className="text-lg font-semibold text-gray-900">历史分析</h2>
                  <button
                    onClick={() => setShowHistorySidebar(false)}
                    className="p-1 text-gray-400 hover:text-gray-600"
                  >
                    <ChevronRight className="w-5 h-5" />
                  </button>
                </div>
              </div>

              <div className="p-4 space-y-3">
                {historyItems.length > 0 ? (
                  // 按时间分组显示历史记录
                  (() => {
                    const groupedHistory = historyItems.reduce((groups: { [key: string]: HistoryItem[] }, item) => {
                      const date = new Date(item.timestamp)
                      const today = new Date()
                      const yesterday = new Date(today)
                      yesterday.setDate(yesterday.getDate() - 1)

                      let groupKey = ''
                      if (date.toDateString() === today.toDateString()) {
                        groupKey = '今天'
                      } else if (date.toDateString() === yesterday.toDateString()) {
                        groupKey = '昨天'
                      } else {
                        const diffTime = Math.abs(today.getTime() - date.getTime())
                        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
                        if (diffDays <= 7) {
                          groupKey = '本周'
                        } else {
                          groupKey = '更早'
                        }
                      }

                      if (!groups[groupKey]) {
                        groups[groupKey] = []
                      }
                      groups[groupKey].push(item)
                      return groups
                    }, {})

                    return Object.entries(groupedHistory).map(([groupName, items]) => (
                      <div key={groupName}>
                        <h3 className="text-xs font-medium text-gray-500 mb-2">{groupName}</h3>
                        <div className="space-y-2">
                          {items.map((item, index) => (
                            <button
                              key={item.id}
                              className={`w-full text-left p-3 ${
                                index === 0 && groupName === '今天'
                                  ? 'bg-red-50 border border-red-200'
                                  : 'bg-white border border-gray-200'
                              } rounded-lg hover:bg-gray-50 transition-colors`}
                              onClick={() => {
                                setMessage(item.title)
                                setShowHistorySidebar(false)
                              }}
                            >
                              <div className="text-sm font-medium text-gray-900 truncate">
                                {item.title}
                              </div>
                              <div className="text-xs text-gray-500 mt-1">{item.timestamp}</div>
                            </button>
                          ))}
                        </div>
                      </div>
                    ))
                  })()
                ) : (
                  <div className="text-center text-gray-500 py-8">
                    <div className="text-sm">暂无历史记录</div>
                  </div>
                )}
              </div>
            </div>
          </div>
        )}

        {/* Conditional rendering based on isStarted */}
        {!isStarted ? (
          <WelcomeScreen 
          message={message}
          onMessageChange={setMessage}
          onSend={handleSendMessage}
        />
        ) : (
          <>
            {/* Top Toolbar */}
            <div className="flex-shrink-0 border-b border-gray-200 p-4">
              <div className="flex items-center justify-between">
                <button
                  onClick={() => setShowHistorySidebar(true)}
                  className="flex items-center px-3 py-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors"
                >
                  <Shield className="w-4 h-4 mr-2" />
                  <span className="text-sm">历史分析</span>
                </button>

                <div className="flex items-center space-x-2">
                  <span className="text-sm text-gray-500">声誉风险监测</span>
                  <div className="w-2 h-2 bg-red-500 rounded-full animate-pulse"></div>
                </div>
              </div>
            </div>

            <div className="flex-1 overflow-y-auto p-6 space-y-6">
              {/* 动态聊天消息 */}
              {chatMessages.map((msg) => (
                <div key={msg.id} className={`flex ${msg.role === 'user' ? 'justify-end' : 'justify-start'}`}>
                  <div className="flex items-start space-x-3 max-w-2xl">
                    {msg.role === 'assistant' && (
                      <div className="flex flex-col items-center">
                        <div className="w-8 h-8 bg-gradient-to-r from-red-500 to-orange-500 rounded-full flex items-center justify-center mb-1">
                          <span className="text-white text-sm font-medium">R</span>
                        </div>
                        <span className="text-xs text-gray-500">RiskBot</span>
                      </div>
                    )}
                    <div className={`${
                      msg.role === 'user'
                        ? 'bg-blue-500 text-white'
                        : 'bg-white border border-gray-200'
                    } rounded-lg px-4 py-2`}>
                      <p className={msg.role === 'assistant' ? 'text-gray-700' : ''}>{msg.content}</p>
                    </div>
                    {msg.role === 'user' && (
                      <div className="flex flex-col items-center">
                        <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center mb-1">
                          <span className="text-white text-sm font-medium">U</span>
                        </div>
                        <span className="text-xs text-gray-500">风控专员</span>
                      </div>
                    )}
                  </div>
                </div>
              ))}

              {/* 流式内容显示 */}
              {isLoading && (
                <div className="flex justify-start">
                  <div className="flex items-start space-x-3 max-w-2xl">
                    <div className="flex flex-col items-center">
                      <div className="w-8 h-8 bg-gradient-to-r from-red-500 to-orange-500 rounded-full flex items-center justify-center mb-1">
                        <Loader2 className="w-4 h-4 text-white animate-spin" />
                      </div>
                      <span className="text-xs text-gray-500">RiskBot</span>
                    </div>
                    <div className="bg-white border border-gray-200 rounded-lg px-4 py-2">
                      <div className="flex items-center space-x-2">
                        <Loader2 className="w-4 h-4 text-blue-500 animate-spin" />
                        <span className="text-gray-700">正在分析中...</span>
                      </div>
                      {streamingContent && (
                        <div className="mt-2 text-gray-700">
                          {streamingContent}
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              )}

              <div>
              {/* <div className="space-y-4">
                <div className="flex items-start space-x-3">
                  <div className="flex flex-col items-center">
                    <div className="w-8 h-8 bg-gradient-to-r from-red-500 to-orange-500 rounded-full flex items-center justify-center mb-1">
                      <span className="text-white text-sm font-medium">R</span>
                    </div>
                    <span className="text-xs text-gray-500">RiskBot</span>
                  </div>
                  <div className="flex-1">
                    <div className="bg-white border border-gray-200 rounded-lg p-6">
                      <div className="flex items-center mb-4">
                        <div className="w-1 h-6 bg-red-500 rounded mr-3"></div>
                        <h3 className="text-lg font-semibold text-gray-900">声誉风险评估报告</h3>
                        <div className="ml-auto flex items-center space-x-2">
                          <AlertTriangle className="w-5 h-5 text-red-500" />
                          <span className="text-sm font-medium text-red-600">高风险</span>
                        </div>
                      </div>

                      <div className="text-gray-700 mb-6 leading-relaxed">
                        从2025-01到2025-07，机构【声誉指数】呈现【持续下降】趋势，从78.5降至61.2，下降幅度达22%。主要受到【负面事件频发】、【媒体曝光增加】、【利益相关者情绪恶化】等因素影响。尽管在ESG治理和客户服务方面有所改善，但未能有效遏制声誉风险的恶化趋势。建议立即启动声誉风险应急预案，采取积极的危机公关措施。
                      </div>

                      <div className="space-y-4">
                        <div className="flex items-center text-red-600 mb-3">
                          <div className="w-6 h-6 bg-red-100 rounded-full flex items-center justify-center mr-2">
                            <AlertTriangle className="w-3 h-3" />
                          </div>
                          <span className="font-medium">紧急应对建议：</span>
                        </div>

                        <div className="space-y-4">
                          <div>
                            <p className="font-medium text-gray-900 mb-2">【声誉指数】持续下降，建议：</p>
                            <ol className="list-decimal list-inside space-y-1 text-gray-700 ml-4">
                              <li>立即启动声誉风险应急响应机制，成立危机公关小组。</li>
                              <li>加强媒体沟通，主动发声澄清不实传言。</li>
                              <li>优化客户服务流程，提升客户满意度。</li>
                            </ol>
                          </div>

                          <div>
                            <p className="font-medium text-gray-900 mb-2">【负面事件频发】导致声誉受损，建议：</p>
                            <ol className="list-decimal list-inside space-y-1 text-gray-700 ml-4">
                              <li>建立完善的内控合规体系，从源头防范风险事件。</li>
                              <li>加强员工培训，提升风险意识和专业素养。</li>
                              <li>建立快速响应机制，及时处置突发事件。</li>
                            </ol>
                          </div>
                        </div>
                      </div>

                      <div className="flex items-center justify-between mt-6 pt-4 border-t border-gray-100">
                        <div className="flex items-center space-x-4">
                          <button className="flex items-center text-gray-500 hover:text-gray-700">
                            <Copy className="w-4 h-4 mr-1" />
                            复制
                          </button>
                          <button className="flex items-center text-gray-500 hover:text-gray-700">
                            <Quote className="w-4 h-4 mr-1" />
                            引用
                          </button>
                          <button className="flex items-center text-gray-500 hover:text-gray-700">
                            <RotateCcw className="w-4 h-4 mr-1" />
                            生成应对方案
                          </button>
                        </div>
                        <div className="flex items-center space-x-2">
                          <button className="p-2 text-gray-400 hover:text-green-500">
                            <ThumbsUp className="w-4 h-4" />
                          </button>
                          <button className="p-2 text-gray-400 hover:text-red-500">
                            <ThumbsDown className="w-4 h-4" />
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                
                <div className="flex items-start space-x-3">
                  <div className="flex flex-col items-center">
                    <div className="w-8 h-8 bg-gradient-to-r from-red-500 to-orange-500 rounded-full flex items-center justify-center mb-1">
                      <span className="text-white text-sm font-medium">R</span>
                    </div>
                    <span className="text-xs text-gray-500">RiskBot</span>
                  </div>
                  <div className="flex-1">
                    <div className="bg-white border border-gray-200 rounded-lg p-6">
                      <div className="flex items-center justify-between mb-4">
                        <div>
                          <p className="text-sm text-gray-600 mb-2">
                            已成功采集2025-01到2025-07的声誉风险监测数据，完成多维度风险评估分析
                          </p>
                          <h3 className="text-lg font-semibold text-gray-900">声誉风险监测数据</h3>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Eye className="w-4 h-4 text-gray-400" />
                          <Info className="w-4 h-4 text-gray-400" />
                          <Download className="w-4 h-4 text-gray-400" />
                          <ChevronUp className="w-4 h-4 text-gray-400" />
                        </div>
                      </div>

                      <div className="overflow-x-auto">
                        <table className="w-full">
                          <thead>
                            <tr className="border-b border-gray-200">
                              <th className="text-left py-3 px-4 font-medium text-gray-700">
                                统计时间 <ChevronDown className="w-3 h-3 inline ml-1" />
                              </th>
                              <th className="text-left py-3 px-4 font-medium text-gray-700">
                                声誉指数 <ChevronDown className="w-3 h-3 inline ml-1" />
                              </th>
                              <th className="text-left py-3 px-4 font-medium text-gray-700">
                                负面事件 <ChevronDown className="w-3 h-3 inline ml-1" />
                              </th>
                              <th className="text-left py-3 px-4 font-medium text-gray-700">
                                媒体曝光 <ChevronDown className="w-3 h-3 inline ml-1" />
                              </th>
                              <th className="text-left py-3 px-4 font-medium text-gray-700">
                                利益相关者情绪 <ChevronDown className="w-3 h-3 inline ml-1" />
                              </th>
                            </tr>
                          </thead>
                          <tbody>
                            {tableData.map((row, index) => (
                              <tr key={index} className="border-b border-gray-100 hover:bg-gray-50">
                                <td className="py-3 px-4 text-gray-900">{row.time}</td>
                                <td className="py-3 px-4">
                                  <span
                                    className={`font-medium ${Number.parseFloat(row.reputationIndex) < 65 ? "text-red-600" : Number.parseFloat(row.reputationIndex) < 75 ? "text-yellow-600" : "text-green-600"}`}
                                  >
                                    {row.reputationIndex}
                                  </span>
                                </td>
                                <td className="py-3 px-4">
                                  <span
                                    className={`font-medium ${Number.parseInt(row.negativeEvents) > 15 ? "text-red-600" : Number.parseInt(row.negativeEvents) > 10 ? "text-yellow-600" : "text-green-600"}`}
                                  >
                                    {row.negativeEvents}
                                  </span>
                                </td>
                                <td className="py-3 px-4 text-gray-900">{row.mediaExposure}</td>
                                <td className="py-3 px-4">
                                  <span
                                    className={`px-2 py-1 text-xs rounded-full ${row.stakeholderSentiment === "负面"
                                        ? "bg-red-100 text-red-800"
                                        : row.stakeholderSentiment === "中性"
                                          ? "bg-yellow-100 text-yellow-800"
                                          : "bg-green-100 text-green-800"
                                      }`}
                                  >
                                    {row.stakeholderSentiment}
                                  </span>
                                </td>
                              </tr>
                            ))}
                          </tbody>
                        </table>
                      </div>

                      <div className="flex items-center space-x-3 mt-4">
                        <button className="flex items-center px-4 py-2 border border-red-500 text-red-500 rounded-full hover:bg-red-50">
                          <FileText className="w-4 h-4 mr-2" />
                          风险解读
                        </button>
                        <button className="flex items-center px-4 py-2 border border-blue-500 text-blue-500 rounded-full hover:bg-blue-50">
                          <BarChart3 className="w-4 h-4 mr-2" />
                          趋势图表
                        </button>
                        <button className="flex items-center px-4 py-2 border border-orange-500 text-orange-500 rounded-full hover:bg-orange-50">
                          <TrendingUp className="w-4 h-4 mr-2" />
                          影响归因
                        </button>
                        <button className="flex items-center px-4 py-2 border border-purple-500 text-purple-500 rounded-full hover:bg-purple-50">
                          <AlertTriangle className="w-4 h-4 mr-2" />
                          风险预警
                        </button>
                      </div>
                    </div>
                  </div>
                </div>

                
                <div className="flex items-start space-x-3">
                  <div className="flex flex-col items-center">
                    <div className="w-8 h-8 bg-gradient-to-r from-red-500 to-orange-500 rounded-full flex items-center justify-center mb-1">
                      <span className="text-white text-sm font-medium">R</span>
                    </div>
                    <span className="text-xs text-gray-500">RiskBot</span>
                  </div>
                  <div className="flex-1">
                    <div className="bg-white border border-gray-200 rounded-lg p-6">
                      <div className="flex items-center justify-between mb-4">
                        <h3 className="text-lg font-semibold text-gray-900">声誉风险归因分析</h3>
                        <Download className="w-4 h-4 text-gray-400" />
                      </div>

                      <div className="mb-6">
                        <div className="flex items-center space-x-4 mb-4">
                          <span className="text-sm text-gray-600">分析指标</span>
                          <select className="border border-gray-300 rounded px-3 py-1 text-sm">
                            <option>声誉指数</option>
                          </select>
                          <ChevronDown className="w-4 h-4 text-gray-400" />
                          <span className="text-sm text-gray-500">（评分范围：0-100）</span>
                        </div>

                        <div className="flex items-center space-x-4 text-sm text-gray-600 mb-4">
                          <span>分析时间</span>
                          <span className="font-medium">2025-01</span>
                          <span>至</span>
                          <span className="font-medium">2025-07</span>
                          <div className="w-4 h-4 bg-gray-200 rounded"></div>
                          <span>风险维度</span>
                          <select className="border border-gray-300 rounded px-2 py-1 text-xs">
                            <option>媒体舆情,监管处罚,客户投诉...</option>
                          </select>
                          <Info className="w-4 h-4 text-gray-400" />
                        </div>
                      </div>

                      <div className="space-y-6">
                        <div className="border-l-4 border-red-500 pl-4">
                          <h4 className="font-medium text-gray-900 mb-4">风险概况</h4>

                          <div className="grid grid-cols-4 gap-6 mb-4">
                            <div className="text-center">
                              <div className="flex items-center justify-center mb-2">
                                <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                                  <CheckCircle className="w-4 h-4 text-green-600" />
                                </div>
                              </div>
                              <div className="text-sm text-gray-500">基期值</div>
                              <div className="text-sm text-gray-600">2025-01</div>
                              <div className="text-lg font-bold text-green-600">78.5分</div>
                            </div>

                            <div className="text-center">
                              <div className="flex items-center justify-center mb-2">
                                <div className="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center">
                                  <AlertTriangle className="w-4 h-4 text-red-600" />
                                </div>
                              </div>
                              <div className="text-sm text-gray-500">当期值</div>
                              <div className="text-sm text-gray-600">2025-07</div>
                              <div className="text-lg font-bold text-red-600">61.2分</div>
                            </div>

                            <div className="text-center">
                              <div className="flex items-center justify-center mb-2">
                                <div className="w-8 h-8 bg-orange-100 rounded-full flex items-center justify-center">
                                  <TrendingUp className="w-4 h-4 text-orange-600 rotate-180" />
                                </div>
                              </div>
                              <div className="text-sm text-gray-500">变化量</div>
                              <div className="text-lg font-bold text-red-600">↓ -17.3分</div>
                            </div>

                            <div className="text-center">
                              <div className="flex items-center justify-center mb-2">
                                <div className="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center">
                                  <span className="text-red-600 font-bold text-xs">%</span>
                                </div>
                              </div>
                              <div className="text-sm text-gray-500">变化率</div>
                              <div className="text-lg font-bold text-red-600">↓ -22.0%</div>
                            </div>
                          </div>

                          <p className="text-sm text-gray-700 mb-6">
                            声誉指数在这期间从78.5分下降至61.2分，降幅达17.3分，变化率为-22.0%。当前处于
                            <span className="font-medium text-red-600">高风险</span>状态，需要立即采取应对措施。
                          </p>
                        </div>

                        <div className="border-l-4 border-red-500 pl-4">
                          <h4 className="font-medium text-gray-900 mb-4">风险因素归因</h4>

                          <p className="text-sm text-gray-700 mb-4">
                            声誉指数共有12个影响维度，已识别出负面影响和正面影响排名前三的风险因素
                            <button className="text-red-500 hover:underline ml-2">查看详细报告</button>
                          </p>

                          <div className="space-y-4">
                            <div>
                              <h5 className="font-medium text-gray-900 mb-3">负面影响因素 TOP3</h5>
                              <div className="space-y-2">
                                <div className="flex justify-between items-center p-3 bg-red-50 rounded">
                                  <span className="text-sm text-gray-700">监管处罚事件=重大违规</span>
                                  <span className="text-sm font-medium text-red-600">-8.5分(-48.8%)</span>
                                </div>
                                <div className="flex justify-between items-center p-3 bg-red-50 rounded">
                                  <span className="text-sm text-gray-700">媒体负面报道=数据泄露</span>
                                  <span className="text-sm font-medium text-red-600">-5.2分(-29.9%)</span>
                                </div>
                                <div className="flex justify-between items-center p-3 bg-red-50 rounded">
                                  <span className="text-sm text-gray-700">客户投诉激增=服务质量</span>
                                  <span className="text-sm font-medium text-red-600">-3.8分(-21.8%)</span>
                                </div>
                              </div>
                            </div>

                            <div>
                              <h5 className="font-medium text-gray-900 mb-3">正面缓解因素 TOP3</h5>
                              <div className="space-y-2">
                                <div className="flex justify-between items-center p-3 bg-green-50 rounded">
                                  <span className="text-sm text-gray-700">ESG评级提升=环境治理</span>
                                  <span className="text-sm font-medium text-green-600">+1.8分(10.4%)</span>
                                </div>
                                <div className="flex justify-between items-center p-3 bg-green-50 rounded">
                                  <span className="text-sm text-gray-700">公益活动增加=社会责任</span>
                                  <span className="text-sm font-medium text-green-600">+1.2分(6.9%)</span>
                                </div>
                                <div className="flex justify-between items-center p-3 bg-green-50 rounded">
                                  <span className="text-sm text-gray-700">产品创新获奖=技术领先</span>
                                  <span className="text-sm font-medium text-green-600">+0.9分(5.2%)</span>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                
                <div className="flex items-start space-x-3">
                  <div className="flex flex-col items-center">
                    <div className="w-8 h-8 bg-gradient-to-r from-red-500 to-orange-500 rounded-full flex items-center justify-center mb-1">
                      <span className="text-white text-sm font-medium">R</span>
                    </div>
                    <span className="text-xs text-gray-500">RiskBot</span>
                  </div>
                  <div className="flex-1">
                    <div className="bg-white border border-gray-200 rounded-lg p-6">
                      <div className="flex items-center justify-between mb-4">
                        <div>
                          <p className="text-sm text-gray-600 mb-2">已完成声誉风险趋势可视化分析</p>
                        </div>
                        <div className="flex items-center space-x-2">
                          <TrendingUp className="w-4 h-4 text-gray-400" />
                          <BarChart3 className="w-4 h-4 text-gray-400" />
                          <AlertTriangle className="w-4 h-4 text-red-400" />
                          <Download className="w-4 h-4 text-gray-400" />
                          <ChevronUp className="w-4 h-4 text-gray-400" />
                        </div>
                      </div>

                      <div className="mb-4">
                        <div className="flex items-center space-x-2">
                          <span className="text-sm text-gray-600">风险指标</span>
                          <select
                            value={selectedMetric}
                            onChange={(e) => setSelectedMetric(e.target.value)}
                            className="border border-gray-300 rounded px-3 py-1 text-sm"
                          >
                            <option value="声誉指数">声誉指数</option>
                            <option value="负面事件">负面事件数量</option>
                            <option value="媒体曝光">媒体曝光度</option>
                          </select>
                          <ChevronDown className="w-4 h-4 text-red-500" />
                        </div>
                        <p className="text-xs text-gray-500 mt-2">（评分范围：0-100分，低于65分为高风险）</p>
                      </div>

                      <div className="h-64">
                        <ResponsiveContainer width="100%" height="100%">
                          <AreaChart data={chartData}>
                            <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                            <XAxis
                              dataKey="month"
                              axisLine={false}
                              tickLine={false}
                              tick={{ fontSize: 12, fill: "#666" }}
                            />
                            <YAxis
                              axisLine={false}
                              tickLine={false}
                              tick={{ fontSize: 12, fill: "#666" }}
                              domain={[50, 85]}
                              tickFormatter={(value) => `${value}分`}
                            />
                            <Area
                              type="monotone"
                              dataKey="value"
                              stroke="#ef4444"
                              strokeWidth={2}
                              fill="#ef4444"
                              fillOpacity={0.1}
                            />
                          </AreaChart>
                        </ResponsiveContainer>
                      </div>

                      
                      <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-lg">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-2">
                            <AlertTriangle className="w-4 h-4 text-red-500" />
                            <span className="text-sm font-medium text-red-700">当前风险等级：高风险</span>
                          </div>
                          <span className="text-xs text-red-600">建议立即采取应对措施</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                
                <div className="flex items-start space-x-3">
                  <div className="flex flex-col items-center">
                    <div className="w-8 h-8 bg-gradient-to-r from-red-500 to-orange-500 rounded-full flex items-center justify-center mb-1">
                      <Loader2 className="w-4 h-4 text-white animate-spin" />
                    </div>
                    <div className="flex items-center space-x-1">
                      <span className="text-xs text-gray-500">RiskBot</span>
                      <Loader2 className="w-3 h-3 text-gray-400 animate-spin" />
                    </div>
                  </div>
                  <div className="bg-orange-50 border border-orange-200 rounded-lg p-4">
                    <div className="flex items-center space-x-2">
                      <Loader2 className="w-4 h-4 text-orange-500 animate-spin" />
                      <span className="text-orange-700 text-sm">正在生成风险应对策略...</span>
                    </div>
                  </div>
                </div>
              </div> */}
              </div>
            </div>

            {/* Chat Input */}
            <div className="border-t border-gray-200 p-4">
              <div className="bg-gray-50 rounded-2xl p-4">
                <div className="flex items-center justify-between mb-3">
                  <span className="text-gray-500 text-sm">发送消息给 RiskBot</span>
                  {/* Icons moved to top right */}
                  {/* <div className="flex items-center space-x-2">
                    <button className="p-1 text-gray-400 hover:text-gray-600">
                      <Paperclip className="w-4 h-4" />
                    </button>
                    <button className="p-1 text-gray-400 hover:text-gray-600">
                      <Languages className="w-4 h-4" />
                    </button>
                    <button className="p-1 text-gray-400 hover:text-gray-600">
                      <AlertTriangle className="w-4 h-4" />
                    </button>
                    <button className="p-1 text-gray-400 hover:text-gray-600">
                      <Shield className="w-4 h-4" />
                    </button>
                  </div> */}
                </div>

                <div className="flex items-center">
                  <input
                    type="text"
                    value={message}
                    onChange={(e) => setMessage(e.target.value)}
                    placeholder="输入您的风险分析需求..."
                    className="flex-1 bg-transparent border-none outline-none text-gray-700"
                    onKeyDown={(e) => e.key === "Enter" && handleSendMessage()}
                  />
                  <button className="p-2 text-gray-400 hover:text-gray-600 mr-2">
                    <Mic className="w-5 h-5" />
                  </button>
                  <button onClick={handleSendMessage} className="p-2 text-red-500 hover:text-red-600">
                    <Send className="w-5 h-5" />
                  </button>
                </div>
              </div>
            </div>
          </>
        )}
      </div>

      {/* Right Research Think Panel - Only show when analysis is started */}
      {isStarted && shouldShowThinkPanel && (
        <div className="w-1/3 bg-white flex flex-col">
          {/* Header - Fixed */}
          <div className="flex-shrink-0 p-4 border-b border-gray-200">
            <div className="flex justify-between items-center">
              <h1 className="text-lg font-semibold text-gray-900">声誉风险分析面板</h1>
              <button
                onClick={() => setHideThinkPanel(true)}
                className="flex items-center px-2 py-1 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors"
              >
                <EyeOff className="w-4 h-4 mr-1" />
                <span className="text-sm">隐藏</span>
              </button>
            </div>

            {/* Status Tags */}
            <div className="flex space-x-2 mt-3">
              <span className="px-2 py-1 text-xs bg-red-100 text-red-800 rounded-full">风险分析中</span>
              <span className="px-2 py-1 text-xs bg-orange-100 text-orange-800 rounded-full">实时监测</span>
            </div>
          </div>

          {/* Scrollable Content */}
          <div className="flex-1 overflow-y-auto p-4 space-y-4">
            {currentSession && (
              <div className="space-y-4">
                {/* Session Header */}
                <div className="bg-red-50 border border-red-200 rounded-lg p-3">
                  <h3 className="text-sm font-medium text-red-900 mb-2">分析任务</h3>
                  <p className="text-sm text-red-800">{currentSession.query}</p>
                </div>

                {/* Progress Overview */}
                <div className="bg-white border border-gray-200 rounded-lg p-4">
                  <div className="flex items-center justify-between mb-4">
                    <h4 className="text-sm font-medium text-gray-900">• 分析进度</h4>
                    <div className="text-xs text-gray-500">
                      步骤 {currentSession.currentStep}/{currentSession.totalSteps}
                    </div>
                  </div>

                  {/* Progress Bar */}
                  <div className="mb-4">
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-sm text-gray-600">整体进度</span>
                      <span className="text-sm font-medium text-gray-900">{currentSession.overallProgress}%</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div
                        className="bg-blue-500 h-2 rounded-full transition-all duration-300"
                        style={{ width: `${currentSession.overallProgress}%` }}
                      ></div>
                    </div>
                  </div>

                  {/* Status Statistics */}
                  <div className="grid grid-cols-3 gap-2 mb-4 text-center">
                    <div>
                      <div className="text-lg font-bold text-purple-600">{currentSession.totalSteps}</div>
                      <div className="text-xs text-gray-500">总步骤</div>
                    </div>
                    <div>
                      <div className="text-lg font-bold text-blue-600">{currentSession.currentStep}</div>
                      <div className="text-xs text-gray-500">当前步骤</div>
                    </div>
                    <div>
                      <div className="text-lg font-bold text-green-600">
                        {currentSession.events.filter(e => e.status === "completed").length}
                      </div>
                      <div className="text-xs text-gray-500">已完成</div>
                    </div>
                  </div>

                  {/* Event Timeline */}
                  <div className="space-y-3">
                    <h5 className="text-sm font-medium text-gray-900 mb-3">执行时间线</h5>
                    <div className="relative">
                      {/* Vertical Line */}
                      <div className="absolute left-4 top-0 bottom-0 w-0.5 bg-gray-200"></div>

                      <div className="space-y-4">
                        {currentSession.events.map((event, index) => (
                          <div key={index} className="relative flex items-start">
                            {/* Event Node */}
                            <div className={`relative z-10 flex items-center justify-center w-8 h-8 rounded-full border-2 ${
                              event.status === 'completed'
                                ? 'bg-green-100 border-green-500 text-green-600'
                                : event.status === 'processing'
                                ? 'bg-blue-100 border-blue-500 text-blue-600'
                                : 'bg-red-100 border-red-500 text-red-600'
                            }`}>
                              <span className="text-xs">{event.icon}</span>
                            </div>

                            {/* Event Content */}
                            <div className="flex-1 ml-3">
                              <div className="flex items-center justify-between mb-1">
                                <div className="flex items-center space-x-2">
                                  <span className="text-sm font-medium text-gray-900">
                                    步骤 {event.step}
                                  </span>
                                  <span className={`px-2 py-1 text-xs rounded-full ${
                                    event.status === 'completed'
                                      ? 'bg-green-100 text-green-800'
                                      : event.status === 'processing'
                                      ? 'bg-blue-100 text-blue-800'
                                      : 'bg-red-100 text-red-800'
                                  }`}>
                                    {event.status === 'completed' ? '已完成' :
                                     event.status === 'processing' ? '进行中' : '错误'}
                                  </span>
                                </div>
                                <span className="text-xs text-gray-500">
                                  {new Date(event.timestamp).toLocaleTimeString()}
                                </span>
                              </div>

                              <p className="text-sm text-gray-700 mb-2">{event.message}</p>

                              <div className="flex items-center space-x-3">
                                <span className="text-xs text-gray-500">
                                  代理: {event.agent}
                                </span>
                                <div className="flex items-center space-x-1">
                                  <div className="w-16 bg-gray-200 rounded-full h-1.5">
                                    <div
                                      className={`h-1.5 rounded-full ${
                                        event.status === 'completed' ? 'bg-green-500' :
                                        event.status === 'processing' ? 'bg-blue-500' : 'bg-red-500'
                                      }`}
                                      style={{ width: `${event.progress}%` }}
                                    ></div>
                                  </div>
                                  <span className="text-xs text-gray-500">{event.progress}%</span>
                                </div>
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Show/Hide Think Panel Button when hidden */}
      {isStarted && !shouldShowThinkPanel && (
        <button
          onClick={() => setHideThinkPanel(false)}
          className="fixed top-4 right-4 bg-red-500 text-white p-2 rounded-lg shadow-lg hover:bg-red-600 transition-colors z-10"
        >
          <ChevronRight className="w-5 h-5" />
        </button>
      )}
    </div>
  )
}
