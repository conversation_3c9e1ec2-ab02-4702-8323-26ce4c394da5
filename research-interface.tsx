"use client"

import { useState, useEffect, useRef, useCallback } from "react"
import React from 'react';
import {
  <PERSON>Off,
  Loader2,
  ChevronRight,
  Paperclip,
  Languages,
  Mic,
  Send,
  AlertTriangle,
  Shield,
  Newspaper,
  Users,
  Building2,
  Globe,
  MessageCircle,
  BarChart,
  Bell,
} from "lucide-react"


// 数据类型定义
interface ChatMessage {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  timestamp: Date;
}



interface QuickAction {
  id: number;
  title: string;
  description: string;
  icon: React.ReactNode;
  category: string;
  query: string;
}

interface HistoryItem {
  id: string;
  title: string;
  timestamp: string;
  category: string;
}

interface ProgressEvent {
  timestamp: string;
  agent: string;
  event_type: string;
  step: number;
  progress: number;
  status: 'processing' | 'completed' | 'error';
  message: string;
  icon: string;
}

interface ResearchSession {
  id: string;
  query: string;
  events: ProgressEvent[];
  currentStep: number;
  totalSteps: number;
  overallProgress: number;
  status: 'active' | 'completed' | 'error';
}

// 声誉风险评估相关类型定义
interface AgentEvent {
  agent: string;
  status: 'started' | 'completed';
  message: string;
  timestamp: string;
  data?: {
    planning_summary?: string;
    tasks_count?: number;
    risk_dimensions?: string[];
    entities_count?: number;
    sentiment_data_count?: number;
    collection_status?: string;
    processed_data?: any[];
    chart_data?: any;
    processed_data_count?: number;
    risk_factors_count?: number;
    sentiment_distribution?: any;
    risk_level?: string;
    analysis_conclusion?: string;
    report_status?: string;
    executive_summary?: string;
    final_report?: any;
  };
}

export default function ReputationRiskInterface() {
  const [hideThinkPanel, setHideThinkPanel] = useState(false)

  const [message, setMessage] = useState("")
  const [windowWidth, setWindowWidth] = useState(0)
  const [showHistorySidebar, setShowHistorySidebar] = useState(false)
  const [isStarted, setIsStarted] = useState(false)

  // 动态数据状态
  const [quickActions, setQuickActions] = useState<QuickAction[]>([])
  const [researchSessions, setResearchSessions] = useState<ResearchSession[]>([])
  const [currentSession, setCurrentSession] = useState<ResearchSession | null>(null)
  const [historyItems, setHistoryItems] = useState<HistoryItem[]>([])
  const [chatMessages, setChatMessages] = useState<ChatMessage[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [streamingContent, setStreamingContent] = useState("")
  const [systemInfo, setSystemInfo] = useState({
    title: "声誉风险助手",
    greeting: "今日你好",
    subtitle: "我能为你做什么？",
    description: "声誉风险助手是专为金融机构设计的智能风险分析系统。我可以帮助您实时监测声誉风险、分析负面事件影响、评估媒体舆情、追踪利益相关者情绪变化，并提供专业的风险应对策略。通过AI驱动的多维度分析，为您的机构声誉管理提供全方位的决策支持。"
  })

  // Handle window resize for responsive layout
  useEffect(() => {
    const handleResize = () => {
      setWindowWidth(window.innerWidth)
    }

    handleResize()
    window.addEventListener("resize", handleResize)
    return () => window.removeEventListener("resize", handleResize)
  }, [])

  // Auto-hide think panel when screen is too narrow
  const shouldShowThinkPanel = !hideThinkPanel && windowWidth > 1200

  // 简单的markdown格式化函数
  const formatMarkdownContent = (content: string): string => {
    return content
      .replace(/^## (.*$)/gm, '\n📋 $1\n')  // 二级标题
      .replace(/^### (.*$)/gm, '\n📌 $1\n') // 三级标题
      .replace(/^\d+\. (.*$)/gm, '   $&')    // 数字列表缩进
      .replace(/^- (.*$)/gm, '   • $1')      // 无序列表
  }

  // 流式数据处理函数
  const processStreamData = async (userQuery: string): Promise<string> => {
    setIsLoading(true)
    setStreamingContent("")
    let finalContent = ""

    const param = {
        "conversation_id": "6837cfa1f2638c19a1d04d7d221",
        "user_id": 10,
        "user_name": "超级管理员10",
        "app_info": "chat2shengyuAgent",
        "messages": [
            {
                "id": "6837cfa1f2638c19a1d04d7d-17484881034052",
                "role": "user",
                "content": "分析三星财产保险（中国）有限公司的声誉风险"
            }
        ]
    }

    try {
      const response = await fetch('http://localhost:8800/api/app/chat/chat2shengyuAgent', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'f1cba73fdcae277af2698d25da50b443',
        },
        body: JSON.stringify(param),
      })

      if (!response.ok) {
        throw new Error('Network response was not ok')
      }

      const reader = response.body?.getReader()
      const decoder = new TextDecoder()

      if (reader) {
        while (true) {
          const { done, value } = await reader.read()
          if (done) break

          const chunk = decoder.decode(value)
          const lines = chunk.split('\n')

          let currentEventType = ''

          for (const line of lines) {
            if (line.trim() === '') continue

            // 处理 Server-Sent Events 格式
            if (line.startsWith('event: ')) {
              // 捕获事件类型
              currentEventType = line.slice(7).trim()
              continue
            }

            if (line.startsWith('data: ')) {
              try {
                const dataStr = line.slice(6).trim()

                // 处理不同类型的事件数据
                if (currentEventType === 'end' || dataStr === '声誉风险评估完成' || dataStr === 'Stream has ended') {
                  // 处理结束事件
                  const endContent = `\n✅ ${dataStr}\n`
                  setStreamingContent(prev => prev + endContent)
                  finalContent += endContent

                  setIsLoading(false)
                  setCurrentSession(prevSession => {
                    if (!prevSession) return prevSession
                    return {
                      ...prevSession,
                      status: 'completed' as const,
                      overallProgress: 100
                    }
                  })
                  continue
                }

                const data = JSON.parse(dataStr)

                // 根据事件类型处理数据
                console.log('📨 接收到事件:', currentEventType, data)

                // 处理模块状态事件
                if (currentEventType === 'moduleStatus' && data.status && data.name && data.progress !== undefined) {
                  console.log('📊 接收到模块状态:', data)

                  // 更新流式内容显示
                  const statusText = data.status === 'starting' ? '启动' :
                                   data.status === 'analyzing' ? '分析中' :
                                   data.status === 'initialized' ? '初始化完成' : data.status
                  const progressText = data.is_need_full_workflow ? ' (需要完整工作流)' : ''
                  const newContent = `🔄 ${data.name}: ${statusText}${progressText} (${data.progress}%)\n`
                  setStreamingContent(prev => prev + newContent)
                  finalContent += newContent

                  // 创建进度事件并更新会话
                  const progressEvent: ProgressEvent = {
                    timestamp: new Date().toISOString(),
                    agent: data.name,
                    event_type: 'moduleStatus',
                    step: data.progress <= 10 ? 1 : data.progress <= 25 ? 2 : data.progress <= 50 ? 3 : 4,
                    progress: data.progress,
                    status: data.status === 'starting' ? 'processing' :
                           data.status === 'analyzing' ? 'processing' :
                           data.status === 'initialized' ? 'completed' : 'processing',
                    message: `${data.name}: ${statusText}`,
                    icon: data.status === 'starting' ? '🚀' :
                          data.status === 'analyzing' ? '🔍' :
                          data.status === 'initialized' ? '✅' : '⚙️'
                  }

                  setCurrentSession(prevSession => {
                    if (!prevSession) return prevSession
                    return {
                      ...prevSession,
                      events: [...prevSession.events, progressEvent],
                      currentStep: progressEvent.step,
                      overallProgress: data.progress,
                      status: 'active' as const
                    }
                  })
                }

                // 处理智能体事件
                if (currentEventType === 'agent' && data.event) {
                  console.log('🤖 接收到智能体事件:', data)

                  let agentEvent
                  try {
                    // 解析嵌套的事件字符串 - 使用安全的JSON解析
                    if (typeof data.event === 'string') {
                      // 处理类似 "{'agent': 'planning', 'status': 'started', ...}" 格式的字符串
                      // 将单引号替换为双引号以符合JSON格式
                      const jsonStr = data.event.replace(/'/g, '"')
                      agentEvent = JSON.parse(jsonStr)
                    } else {
                      agentEvent = data.event
                    }
                  } catch (e) {
                    console.error('解析智能体事件失败:', e, '原始数据:', data.event)
                    continue
                  }

                  // 更新流式内容显示
                  const agentName = agentEvent.agent === 'planning' ? '📋 规划智能体' :
                                  agentEvent.agent === 'data_collection' ? '📊 数据收集智能体' :
                                  agentEvent.agent === 'risk_analysis' ? '🔍 风险分析智能体' :
                                  agentEvent.agent === 'report_generation' ? '📄 报告生成智能体' : agentEvent.agent
                  const statusIcon = agentEvent.status === 'started' ? '🚀' : '✅'
                  const newContent = `${statusIcon} ${agentName}: ${agentEvent.message}\n`
                  setStreamingContent(prev => prev + newContent)
                  finalContent += newContent

                  // 创建进度事件 - 根据智能体类型计算步骤和进度
                  let step = 1
                  let progress = 20

                  // 根据智能体类型确定步骤和进度
                  switch (agentEvent.agent) {
                    case 'planning':
                      step = 1
                      progress = agentEvent.status === 'started' ? 15 : 25
                      break
                    case 'data_collection':
                      step = 2
                      progress = agentEvent.status === 'started' ? 30 : 50
                      break
                    case 'risk_analysis':
                      step = 3
                      progress = agentEvent.status === 'started' ? 55 : 75
                      break
                    case 'report_generation':
                      step = 4
                      progress = agentEvent.status === 'started' ? 80 : 95
                      break
                    default:
                      step = 1
                      progress = agentEvent.status === 'started' ? 10 : 20
                  }

                  const progressEvent: ProgressEvent = {
                    timestamp: agentEvent.timestamp || new Date().toISOString(),
                    agent: agentEvent.agent,
                    event_type: 'agent',
                    step: step,
                    progress: progress,
                    status: agentEvent.status === 'started' ? 'processing' :
                           agentEvent.status === 'completed' ? 'completed' : 'processing',
                    message: agentEvent.message,
                    icon: agentEvent.status === 'started' ? '🚀' :
                          agentEvent.status === 'completed' ? '✅' : '⚙️'
                  }

                  setCurrentSession(prevSession => {
                    if (!prevSession) return prevSession
                    return {
                      ...prevSession,
                      events: [...prevSession.events, progressEvent],
                      currentStep: progressEvent.step,
                      overallProgress: progressEvent.progress,
                      status: 'active' as const
                    }
                  })

                  // 如果有数据，处理结构化数据
                  if (agentEvent.data) {
                    // 处理规划数据
                    if (agentEvent.data.planning_summary) {
                      let content = `\n📋 规划详情: ${agentEvent.data.planning_summary}\n`
                      if (agentEvent.data.tasks_count) {
                        content += `   • 任务数量: ${agentEvent.data.tasks_count}\n`
                      }
                      if (agentEvent.data.risk_dimensions && agentEvent.data.risk_dimensions.length > 0) {
                        content += `   • 风险维度: ${agentEvent.data.risk_dimensions.join(', ')}\n`
                      }
                      setStreamingContent(prev => prev + content)
                      finalContent += content
                    }

                    // 处理数据收集结果
                    if (agentEvent.data.entities_count !== undefined) {
                      const status = agentEvent.data.collection_status || '完成'
                      const statusIcon = status === 'failed' ? '❌' : '✅'
                      const statusText = status === 'failed' ? '失败' : '成功'
                      const content = `\n📊 数据收集${statusText}: 实体数量 ${agentEvent.data.entities_count}，情感数据 ${agentEvent.data.sentiment_data_count} 条 ${statusIcon}\n`
                      setStreamingContent(prev => prev + content)
                      finalContent += content
                    }

                    // 处理风险分析数据
                    if (agentEvent.data.processed_data !== undefined) {
                      const dataCount = agentEvent.data.processed_data_count || agentEvent.data.processed_data.length || 0
                      const riskLevel = Array.isArray(agentEvent.data.risk_level) ?
                        agentEvent.data.risk_level.join(', ') : agentEvent.data.risk_level || '未知'
                      const content = `\n🔍 风险分析完成: 处理数据 ${dataCount} 条，风险等级: ${riskLevel}\n`
                      setStreamingContent(prev => prev + content)
                      finalContent += content

                      // 如果有图表数据，记录日志
                      if (agentEvent.data.chart_data) {
                        console.log('收到图表数据:', agentEvent.data.chart_data)
                      }
                    }

                    // 处理最终报告数据
                    if (agentEvent.data.final_report) {
                      const report = agentEvent.data.final_report
                      let content = `\n📊 ${report.report_type} 生成完成\n`
                      setStreamingContent(prev => prev + content)
                      finalContent += content

                      // 显示报告基本信息
                      if (report.report_id) {
                        const reportInfo = `\n报告ID: ${report.report_id}\n生成时间: ${report.generated_at}\n版本: ${report.version}\n`
                        setStreamingContent(prev => prev + reportInfo)
                        finalContent += reportInfo
                      }

                      // 处理执行摘要的markdown内容
                      if (report.sections && report.sections.executive_summary) {
                        const formattedSummary = formatMarkdownContent(report.sections.executive_summary)
                        const summaryContent = `\n${formattedSummary}\n`
                        setStreamingContent(prev => prev + summaryContent)
                        finalContent += summaryContent
                      }

                      // 处理风险评估数据
                      if (report.sections && report.sections.risk_assessment) {
                        const riskAssessment = report.sections.risk_assessment
                        const riskContent = `\n📊 风险评估结果\n   • 风险等级: ${riskAssessment.risk_level}\n   • 风险评分: ${riskAssessment.risk_score}/100\n   • 置信度: ${riskAssessment.confidence_level}\n   • 风险趋势: ${riskAssessment.risk_trend}\n   • 评估摘要: ${riskAssessment.assessment_summary}\n`
                        setStreamingContent(prev => prev + riskContent)
                        finalContent += riskContent
                      }

                      // 处理详细分析的markdown内容
                      if (report.sections && report.sections.detailed_analysis) {
                        const formattedAnalysis = formatMarkdownContent(report.sections.detailed_analysis)
                        const detailedContent = `\n${formattedAnalysis}\n`
                        setStreamingContent(prev => prev + detailedContent)
                        finalContent += detailedContent
                      }

                      // 处理专业建议
                      if (report.sections && report.sections.professional_recommendations) {
                        const recommendations = report.sections.professional_recommendations
                        if (Array.isArray(recommendations) && recommendations.length > 0) {
                          const recContent = `\n💡 专业建议\n${recommendations.map((rec, index) => `   ${index + 1}. ${rec}`).join('\n')}\n`
                          setStreamingContent(prev => prev + recContent)
                          finalContent += recContent
                        }
                      }
                    }
                  }
                }

                // 处理错误事件
                if (data.error) {
                  console.error('❌ 接收到错误事件:', data.error)
                  const errorContent = `错误: ${data.error}\n`
                  setStreamingContent(prev => prev + errorContent)
                  finalContent += errorContent

                  setCurrentSession(prevSession => {
                    if (!prevSession) return prevSession
                    return {
                      ...prevSession,
                      status: 'error' as const
                    }
                  })
                }

              } catch (e) {
                console.error('Error parsing stream data:', e, 'Raw data:', line)
              }
            }
          }
        }
      }
    } catch (error) {
      console.error('Error fetching stream data:', error)
      const errorContent = `连接错误: ${error}\n`
      setStreamingContent(prev => prev + errorContent)
      finalContent += errorContent

      setCurrentSession(prevSession => {
        if (!prevSession) return prevSession
        return {
          ...prevSession,
          status: 'error' as const
        }
      })
    } finally {
      setIsLoading(false)
    }

    return finalContent
  }

  // 初始化基本的快捷操作（不包含假数据）
  useEffect(() => {
    // 设置基本的快捷操作选项
    setQuickActions([
      {
        id: 1,
        title: "监管处罚事件影响评估",
        description: "分析监管处罚对机构声誉的影响程度",
        icon: <AlertTriangle className="w-5 h-5" />,
        category: "监管风险",
        query: "请分析最近的监管处罚事件对我们机构声誉的影响程度和应对策略",
      },
      {
        id: 2,
        title: "网络舆情负面监测",
        description: "实时监测网络舆情中的负面信息",
        icon: <Globe className="w-5 h-5" />,
        category: "舆情监测",
        query: "分析三星财产保险（中国）有限公司的声誉风险",
      },
      {
        id: 3,
        title: "客户投诉声誉风险",
        description: "评估客户投诉对品牌形象的损害",
        icon: <MessageCircle className="w-5 h-5" />,
        category: "客户关系",
        query: "请分析近期客户投诉情况对我们品牌声誉造成的风险",
      },
      {
        id: 4,
        title: "同业声誉事件传染",
        description: "分析同业负面事件的传染风险",
        icon: <Building2 className="w-5 h-5" />,
        category: "行业风险",
        query: "请分析同业最近发生的声誉事件对我们机构可能产生的传染风险",
      },
    ])

    // 其他数据保持空状态，将通过真实API调用获取
    // chartData, tableData, historyItems 将通过 processStreamData 函数从真实API获取
  }, [])







  const handleSendMessage = async () => {
    if (message.trim()) {
      setIsStarted(true)

      // 创建新的研究会话
      const newSession: ResearchSession = {
        id: `session-${Date.now()}`,
        query: message,
        currentStep: 1,
        totalSteps: 4,
        overallProgress: 0,
        status: 'active',
        events: []
      }

      // 立即更新状态
      console.log('🔧 创建新会话:', newSession)
      setCurrentSession(newSession)
      setResearchSessions(prev => [...prev, newSession])
      console.log('✅ 会话状态已更新')

      // 添加用户消息到聊天记录
      const userMessage: ChatMessage = {
        id: Date.now().toString(),
        role: 'user',
        content: message,
        timestamp: new Date()
      }
      setChatMessages(prev => [...prev, userMessage])

      // 保存当前消息内容
      const currentMessage = message
      setMessage("") // 立即清空输入框

      // 开始流式处理
      const finalContent = await processStreamData(currentMessage)

      // 添加AI回复到聊天记录
      if (finalContent.trim()) {
        const aiMessage: ChatMessage = {
          id: (Date.now() + 1).toString(),
          role: 'assistant',
          content: finalContent,
          timestamp: new Date()
        }
        setChatMessages(prev => [...prev, aiMessage])
      }
    }
  }

  const handleQuickAction = async (action: QuickAction) => {
    setMessage(action.query)
    setIsStarted(true)

    // 创建新的研究会话
    const newSession: ResearchSession = {
      id: `session-${Date.now()}`,
      query: action.query,
      currentStep: 1,
      totalSteps: 4,
      overallProgress: 0,
      status: 'active',
      events: []
    }

    // 立即更新状态
    setCurrentSession(newSession)
    setResearchSessions(prev => [...prev, newSession])

    // 添加用户消息到聊天记录
    const userMessage: ChatMessage = {
      id: Date.now().toString(),
      role: 'user',
      content: action.query,
      timestamp: new Date()
    }
    setChatMessages(prev => [...prev, userMessage])

    // 开始流式处理
    const finalContent = await processStreamData(action.query)

    // 添加AI回复到聊天记录
    if (finalContent.trim()) {
      const aiMessage: ChatMessage = {
        id: (Date.now() + 1).toString(),
        role: 'assistant',
        content: finalContent,
        timestamp: new Date()
      }
      setChatMessages(prev => [...prev, aiMessage])
    }
  }

  // Welcome Screen Component
  const WelcomeScreen = React.memo(({
    message,
    onMessageChange,
    onSend
  }: {
    message: string;
    onMessageChange: (value: string) => void;
    onSend: () => void;
  }) => {
    const containerRef = useRef<HTMLDivElement>(null);
    const inputRef = useRef<HTMLInputElement>(null);
  
    // 稳定的事件处理函数
    const handleChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
      onMessageChange(e.target.value);
    }, []);
  
    // 保持输入框焦点
    useEffect(() => {
      inputRef.current?.focus();
    }, [message]);

    return (
      <div ref={containerRef} className="flex-1 overflow-y-auto p-8">
        <div className="max-w-4xl mx-auto">
          {/* Header */}
          <div className="text-center mb-12">
            <div className="flex items-center justify-center mb-6">
              <div className="w-16 h-16 bg-gradient-to-r from-red-500 to-orange-500 rounded-full flex items-center justify-center mb-4">
                <Shield className="w-8 h-8 text-white" />
              </div>
            </div>
            <h1 className="text-4xl font-bold text-gray-900 mb-4">
              {systemInfo.greeting}，<span className="text-red-600">{systemInfo.title}</span>
            </h1>
            <p className="text-xl text-gray-600 mb-8">{systemInfo.subtitle}</p>

            {/* System Introduction */}
            <div className="bg-gradient-to-r from-red-50 to-orange-50 border border-red-200 rounded-2xl p-6 mb-12">
              <h2 className="text-lg font-semibold text-gray-900 mb-3">系统介绍</h2>
              <p className="text-gray-700 leading-relaxed">
                {systemInfo.description}
              </p>
            </div>
          </div>

          {/* Quick Actions */}
          <div className="mb-8">
            <h3 className="text-lg font-semibold text-gray-900 mb-6 text-center">快捷操作</h3>
            <div className="grid grid-cols-4 gap-4">
              {quickActions.map((action) => (
                <button
                  key={action.id}
                  onClick={() => handleQuickAction(action)}
                  className="group bg-white border border-gray-200 rounded-xl p-4 hover:border-red-300 hover:shadow-lg transition-all duration-200 text-left"
                >
                  <div className="flex items-center mb-3">
                    <div className="w-10 h-10 bg-red-100 rounded-lg flex items-center justify-center text-red-600 group-hover:bg-red-200 transition-colors">
                      {action.icon}
                    </div>
                  </div>
                  <h4 className="font-medium text-gray-900 mb-2 group-hover:text-red-600 transition-colors">
                    {action.title}
                  </h4>
                  <p className="text-sm text-gray-600 mb-3">{action.description}</p>
                  <span className="inline-block px-2 py-1 text-xs bg-gray-100 text-gray-600 rounded-full">
                    {action.category}
                  </span>
                </button>
              ))}
            </div>
          </div>

          {/* Input Area */}
          <div className="bg-white border border-gray-200 rounded-2xl p-6 shadow-sm">
            <div className="flex items-center space-x-4">
              <input
                ref={inputRef}
                type="text"
                value={message}
                onChange={handleChange}
                placeholder="描述您的声誉风险分析需求或提问任何问题..."
                className="flex-1 bg-gray-50 border border-gray-200 rounded-xl px-4 py-3 text-gray-700 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent"
                onKeyDown={(e) => e.key === "Enter" && onSend()}
              />
              <button
                onClick={onSend}
                disabled={!message.trim()}
                className="px-6 py-3 bg-red-500 text-white rounded-xl hover:bg-red-600 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors flex items-center space-x-2"
              >
                <Send className="w-4 h-4" />
                <span>开始分析</span>
              </button>
            </div>

            {/* Input Tools */}
            <div className="flex items-center justify-between mt-4 pt-4 border-t border-gray-100">
              <div className="flex items-center space-x-3">
                <button className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors">
                  <Paperclip className="w-4 h-4" />
                </button>
                <button className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors">
                  <Languages className="w-4 h-4" />
                </button>
                <button className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors">
                  <Mic className="w-4 h-4" />
                </button>
              </div>
              <div className="text-xs text-gray-500">支持文件上传、语音输入和多语言分析</div>
            </div>
          </div>
        </div>
      </div>
    )
  })


  return (
    <div className="flex h-screen bg-white">
      {/* Left Conversation Panel */}
      <div
        className={`${shouldShowThinkPanel ? "w-2/3" : "w-full"} bg-white ${shouldShowThinkPanel ? "border-r border-gray-200" : ""} flex flex-col`}
      >
        {/* History Sidebar Overlay */}
        {showHistorySidebar && (
          <div className="fixed inset-0 bg-black bg-opacity-50 z-50" onClick={() => setShowHistorySidebar(false)}>
            <div className="w-80 h-full bg-white shadow-xl" onClick={(e) => e.stopPropagation()}>
              <div className="p-4 border-b border-gray-200">
                <div className="flex items-center justify-between">
                  <h2 className="text-lg font-semibold text-gray-900">历史分析</h2>
                  <button
                    onClick={() => setShowHistorySidebar(false)}
                    className="p-1 text-gray-400 hover:text-gray-600"
                  >
                    <ChevronRight className="w-5 h-5" />
                  </button>
                </div>
              </div>

              <div className="p-4 space-y-3">
                {historyItems.length > 0 ? (
                  // 按时间分组显示历史记录
                  (() => {
                    const groupedHistory = historyItems.reduce((groups: { [key: string]: HistoryItem[] }, item) => {
                      const date = new Date(item.timestamp)
                      const today = new Date()
                      const yesterday = new Date(today)
                      yesterday.setDate(yesterday.getDate() - 1)

                      let groupKey = ''
                      if (date.toDateString() === today.toDateString()) {
                        groupKey = '今天'
                      } else if (date.toDateString() === yesterday.toDateString()) {
                        groupKey = '昨天'
                      } else {
                        const diffTime = Math.abs(today.getTime() - date.getTime())
                        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
                        if (diffDays <= 7) {
                          groupKey = '本周'
                        } else {
                          groupKey = '更早'
                        }
                      }

                      if (!groups[groupKey]) {
                        groups[groupKey] = []
                      }
                      groups[groupKey].push(item)
                      return groups
                    }, {})

                    return Object.entries(groupedHistory).map(([groupName, items]) => (
                      <div key={groupName}>
                        <h3 className="text-xs font-medium text-gray-500 mb-2">{groupName}</h3>
                        <div className="space-y-2">
                          {items.map((item, index) => (
                            <button
                              key={item.id}
                              className={`w-full text-left p-3 ${
                                index === 0 && groupName === '今天'
                                  ? 'bg-red-50 border border-red-200'
                                  : 'bg-white border border-gray-200'
                              } rounded-lg hover:bg-gray-50 transition-colors`}
                              onClick={() => {
                                setMessage(item.title)
                                setShowHistorySidebar(false)
                              }}
                            >
                              <div className="text-sm font-medium text-gray-900 truncate">
                                {item.title}
                              </div>
                              <div className="text-xs text-gray-500 mt-1">{item.timestamp}</div>
                            </button>
                          ))}
                        </div>
                      </div>
                    ))
                  })()
                ) : (
                  <div className="text-center text-gray-500 py-8">
                    <div className="text-sm">暂无历史记录</div>
                  </div>
                )}
              </div>
            </div>
          </div>
        )}

        {/* Conditional rendering based on isStarted */}
        {!isStarted ? (
          <WelcomeScreen 
          message={message}
          onMessageChange={setMessage}
          onSend={handleSendMessage}
        />
        ) : (
          <>
            {/* Top Toolbar */}
            <div className="flex-shrink-0 border-b border-gray-200 p-4">
              <div className="flex items-center justify-between">
                <button
                  onClick={() => setShowHistorySidebar(true)}
                  className="flex items-center px-3 py-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors"
                >
                  <Shield className="w-4 h-4 mr-2" />
                  <span className="text-sm">历史分析</span>
                </button>

                <div className="flex items-center space-x-2">
                  <span className="text-sm text-gray-500">声誉风险监测</span>
                  <div className="w-2 h-2 bg-red-500 rounded-full animate-pulse"></div>
                </div>
              </div>
            </div>

            <div className="flex-1 overflow-y-auto p-6 space-y-6">
              {/* 动态聊天消息 */}
              {chatMessages.map((msg) => (
                <div key={msg.id} className={`flex ${msg.role === 'user' ? 'justify-end' : 'justify-start'}`}>
                  <div className="flex items-start space-x-3 max-w-2xl">
                    {msg.role === 'assistant' && (
                      <div className="flex flex-col items-center">
                        <div className="w-8 h-8 bg-gradient-to-r from-red-500 to-orange-500 rounded-full flex items-center justify-center mb-1">
                          <span className="text-white text-sm font-medium">R</span>
                        </div>
                        <span className="text-xs text-gray-500">RiskBot</span>
                      </div>
                    )}
                    <div className={`${
                      msg.role === 'user'
                        ? 'bg-blue-500 text-white'
                        : 'bg-white border border-gray-200'
                    } rounded-lg px-4 py-2`}>
                      <p className={msg.role === 'assistant' ? 'text-gray-700' : ''}>{msg.content}</p>
                    </div>
                    {msg.role === 'user' && (
                      <div className="flex flex-col items-center">
                        <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center mb-1">
                          <span className="text-white text-sm font-medium">U</span>
                        </div>
                        <span className="text-xs text-gray-500">风控专员</span>
                      </div>
                    )}
                  </div>
                </div>
              ))}

              {/* 流式内容显示 */}
              {isLoading && (
                <div className="flex justify-start">
                  <div className="flex items-start space-x-3 max-w-2xl">
                    <div className="flex flex-col items-center">
                      <div className="w-8 h-8 bg-gradient-to-r from-red-500 to-orange-500 rounded-full flex items-center justify-center mb-1">
                        <Loader2 className="w-4 h-4 text-white animate-spin" />
                      </div>
                      <span className="text-xs text-gray-500">RiskBot</span>
                    </div>
                    <div className="bg-white border border-gray-200 rounded-lg px-4 py-2">
                      <div className="flex items-center space-x-2">
                        <Loader2 className="w-4 h-4 text-blue-500 animate-spin" />
                        <span className="text-gray-700">正在分析中...</span>
                      </div>
                      {streamingContent && (
                        <div className="mt-2 text-gray-700 whitespace-pre-wrap">
                          {streamingContent}
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              )}

              {/* 真实数据将通过API调用显示在这里 */}
            </div>

            {/* Chat Input */}
            <div className="border-t border-gray-200 p-4">
              <div className="bg-gray-50 rounded-2xl p-4">
                <div className="flex items-center justify-between mb-3">
                  <span className="text-gray-500 text-sm">发送消息给 RiskBot</span>
                  {/* Icons moved to top right */}
                  {/* <div className="flex items-center space-x-2">
                    <button className="p-1 text-gray-400 hover:text-gray-600">
                      <Paperclip className="w-4 h-4" />
                    </button>
                    <button className="p-1 text-gray-400 hover:text-gray-600">
                      <Languages className="w-4 h-4" />
                    </button>
                    <button className="p-1 text-gray-400 hover:text-gray-600">
                      <AlertTriangle className="w-4 h-4" />
                    </button>
                    <button className="p-1 text-gray-400 hover:text-gray-600">
                      <Shield className="w-4 h-4" />
                    </button>
                  </div> */}
                </div>

                <div className="flex items-center">
                  <input
                    type="text"
                    value={message}
                    onChange={(e) => setMessage(e.target.value)}
                    placeholder="输入您的风险分析需求..."
                    className="flex-1 bg-transparent border-none outline-none text-gray-700"
                    onKeyDown={(e) => e.key === "Enter" && handleSendMessage()}
                  />
                  <button className="p-2 text-gray-400 hover:text-gray-600 mr-2">
                    <Mic className="w-5 h-5" />
                  </button>
                  <button onClick={handleSendMessage} className="p-2 text-red-500 hover:text-red-600">
                    <Send className="w-5 h-5" />
                  </button>
                </div>
              </div>
            </div>
          </>
        )}
      </div>

      {/* Right Research Think Panel - Only show when analysis is started */}
      {isStarted && shouldShowThinkPanel && (
        <div className="w-1/3 bg-white flex flex-col">
          {/* Header - Fixed */}
          <div className="flex-shrink-0 p-4 border-b border-gray-200">
            <div className="flex justify-between items-center">
              <h1 className="text-lg font-semibold text-gray-900">声誉风险分析面板</h1>
              <button
                onClick={() => setHideThinkPanel(true)}
                className="flex items-center px-2 py-1 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors"
              >
                <EyeOff className="w-4 h-4 mr-1" />
                <span className="text-sm">隐藏</span>
              </button>
            </div>

            {/* Status Tags */}
            <div className="flex space-x-2 mt-3">
              <span className="px-2 py-1 text-xs bg-red-100 text-red-800 rounded-full">风险分析中</span>
              <span className="px-2 py-1 text-xs bg-orange-100 text-orange-800 rounded-full">实时监测</span>
            </div>
          </div>

          {/* Scrollable Content */}
          <div className="flex-1 overflow-y-auto p-4 space-y-4">
            {currentSession && (
              <div className="space-y-4">
                {/* Session Header */}
                <div className="bg-red-50 border border-red-200 rounded-lg p-3">
                  <h3 className="text-sm font-medium text-red-900 mb-2">分析任务</h3>
                  <p className="text-sm text-red-800">{currentSession.query}</p>
                </div>

                {/* Progress Overview */}
                <div className="bg-white border border-gray-200 rounded-lg p-4">
                  <div className="flex items-center justify-between mb-4">
                    <h4 className="text-sm font-medium text-gray-900">• 分析进度</h4>
                    <div className="text-xs text-gray-500">
                      步骤 {currentSession.currentStep}/{currentSession.totalSteps}
                    </div>
                  </div>

                  {/* Progress Bar */}
                  <div className="mb-4">
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-sm text-gray-600">整体进度</span>
                      <span className="text-sm font-medium text-gray-900">{currentSession.overallProgress}%</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div
                        className="bg-blue-500 h-2 rounded-full transition-all duration-300"
                        style={{ width: `${currentSession.overallProgress}%` }}
                      ></div>
                    </div>
                  </div>

                  {/* Status Statistics */}
                  <div className="grid grid-cols-3 gap-2 mb-4 text-center">
                    <div>
                      <div className="text-lg font-bold text-purple-600">{currentSession.totalSteps}</div>
                      <div className="text-xs text-gray-500">总步骤</div>
                    </div>
                    <div>
                      <div className="text-lg font-bold text-blue-600">{currentSession.currentStep}</div>
                      <div className="text-xs text-gray-500">当前步骤</div>
                    </div>
                    <div>
                      <div className="text-lg font-bold text-green-600">
                        {currentSession.events.filter(e => e.status === "completed").length}
                      </div>
                      <div className="text-xs text-gray-500">已完成</div>
                    </div>
                  </div>

                  {/* Event Timeline */}
                  <div className="space-y-3">
                    <h5 className="text-sm font-medium text-gray-900 mb-3">执行时间线</h5>
                    <div className="relative">
                      {/* Vertical Line */}
                      <div className="absolute left-4 top-0 bottom-0 w-0.5 bg-gray-200"></div>

                      <div className="space-y-4">
                        {currentSession.events.map((event, index) => (
                          <div key={index} className="relative flex items-start">
                            {/* Event Node */}
                            <div className={`relative z-10 flex items-center justify-center w-8 h-8 rounded-full border-2 ${
                              event.status === 'completed'
                                ? 'bg-green-100 border-green-500 text-green-600'
                                : event.status === 'processing'
                                ? 'bg-blue-100 border-blue-500 text-blue-600'
                                : 'bg-red-100 border-red-500 text-red-600'
                            }`}>
                              <span className="text-xs">{event.icon}</span>
                            </div>

                            {/* Event Content */}
                            <div className="flex-1 ml-3">
                              <div className="flex items-center justify-between mb-1">
                                <div className="flex items-center space-x-2">
                                  <span className="text-sm font-medium text-gray-900">
                                    步骤 {event.step}
                                  </span>
                                  <span className={`px-2 py-1 text-xs rounded-full ${
                                    event.status === 'completed'
                                      ? 'bg-green-100 text-green-800'
                                      : event.status === 'processing'
                                      ? 'bg-blue-100 text-blue-800'
                                      : 'bg-red-100 text-red-800'
                                  }`}>
                                    {event.status === 'completed' ? '已完成' :
                                     event.status === 'processing' ? '进行中' : '错误'}
                                  </span>
                                </div>
                                <span className="text-xs text-gray-500">
                                  {new Date(event.timestamp).toLocaleTimeString()}
                                </span>
                              </div>

                              <p className="text-sm text-gray-700 mb-2">{event.message}</p>

                              <div className="flex items-center space-x-3">
                                <span className="text-xs text-gray-500">
                                  代理: {event.agent}
                                </span>
                                <div className="flex items-center space-x-1">
                                  <div className="w-16 bg-gray-200 rounded-full h-1.5">
                                    <div
                                      className={`h-1.5 rounded-full ${
                                        event.status === 'completed' ? 'bg-green-500' :
                                        event.status === 'processing' ? 'bg-blue-500' : 'bg-red-500'
                                      }`}
                                      style={{ width: `${event.progress}%` }}
                                    ></div>
                                  </div>
                                  <span className="text-xs text-gray-500">{event.progress}%</span>
                                </div>
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Show/Hide Think Panel Button when hidden */}
      {isStarted && !shouldShowThinkPanel && (
        <button
          onClick={() => setHideThinkPanel(false)}
          className="fixed top-4 right-4 bg-red-500 text-white p-2 rounded-lg shadow-lg hover:bg-red-600 transition-colors z-10"
        >
          <ChevronRight className="w-5 h-5" />
        </button>
      )}
    </div>
  )
}
